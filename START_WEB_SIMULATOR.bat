@echo off
title FREE Web Driving Simulator - Instant Launch
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                                                              ║
echo  ║        🌐 FREE WEB DRIVING SIMULATOR 🌐                      ║
echo  ║                                                              ║
echo  ║           Instant Launch - No Downloads Needed!              ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎮 Your FREE Realistic Driving Simulator is ready!
echo.
echo 💰 Total Cost: $0.00 (Everything is FREE!)
echo 🌐 Platform: Web browser (works on any device)
echo 📱 Mobile: Touch controls included
echo 🚀 Performance: Instant loading, no installation
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python found - Starting web server...
    goto start_python_server
) else (
    echo ⚠️ Python not found - Using direct browser method...
    goto start_direct_browser
)

:start_python_server
echo.
echo 🚀 Starting local web server...
echo 🌐 This will open your browser automatically
echo ⚠️ Keep this window open while playing
echo.
python run_web_simulator.py
goto end

:start_direct_browser
echo.
echo 🌐 Opening simulator directly in browser...
echo.

REM Try to open the HTML file directly
if exist "index.html" (
    echo ✅ Found simulator file
    echo 🚀 Launching in your default browser...
    start "" "index.html"
    echo.
    echo 🎉 FREE Driving Simulator launched!
    echo.
    echo 🎮 CONTROLS:
    echo   W/A/S/D or Arrow Keys - Drive the car
    echo   Space - Handbrake
    echo   📱 Mobile: Swipe to control
    echo.
    echo 🎯 FEATURES:
    echo   ✅ Realistic car physics
    echo   ✅ Mission-based gameplay  
    echo   ✅ Fuel and damage systems
    echo   ✅ GPS navigation
    echo   ✅ Money management
    echo   ✅ Educational geography
    echo.
    echo 💡 TIP: For best performance, use Chrome or Firefox
    echo.
) else (
    echo ❌ Simulator file not found!
    echo Please make sure index.html is in this folder.
    echo.
)

:end
echo Press any key to close this launcher...
pause >nul
