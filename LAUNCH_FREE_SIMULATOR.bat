@echo off
title FREE Realistic Driving Simulator - Launcher
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                                                              ║
echo  ║        🎮 FREE DRIVING SIMULATOR LAUNCHER 🎮                 ║
echo  ║                                                              ║
echo  ║              Ready to Play - 100%% FREE!                     ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 Your FREE Realistic Driving Simulator is ready!
echo.
echo 💰 Total Cost: $0.00 (Everything is FREE!)
echo 🎮 Engine: Godot 4.2 (FREE alternative to Unity)
echo 🎨 3D Assets: Blender (FREE 3D modeling)
echo 🗺️ Maps: OpenStreetMap (FREE alternative to Google Maps)
echo.

REM Check if Godot exists
if exist "Godot.exe" (
    echo ✅ Godot Engine found
    goto launch_game
) else (
    echo 📥 Godot Engine not found. Let's download it...
    goto download_godot
)

:download_godot
echo.
echo 🌐 Downloading Godot Engine (FREE)...
echo This may take a few minutes depending on your internet speed.
echo.

REM Try to download Godot directly
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri 'https://github.com/godotengine/godot/releases/download/4.2.1-stable/Godot_v4.2.1-stable_win64.exe' -OutFile 'Godot.exe' -UseBasicParsing; Write-Host 'Download completed successfully' } catch { Write-Host 'Download failed, opening browser...' } }"

if exist "Godot.exe" (
    echo ✅ Godot Engine downloaded successfully!
    goto launch_game
) else (
    echo ⚠️ Automatic download failed. Opening browser for manual download...
    start https://godotengine.org/download/
    echo.
    echo 📋 Manual Download Instructions:
    echo 1. Download "Godot Engine - Standard version" for Windows
    echo 2. Save it as "Godot.exe" in this folder: %~dp0
    echo 3. Run this launcher again
    echo.
    pause
    exit /b
)

:launch_game
echo.
echo 🚀 Launching FREE Realistic Driving Simulator...
echo.
echo 🎮 Controls:
echo   W/A/S/D or Arrow Keys - Drive the car
echo   Space - Handbrake
echo   Escape - Pause menu
echo   Mouse - Look around
echo.
echo 🎯 Game Features:
echo   ✅ Realistic car physics
echo   ✅ Mission-based gameplay
echo   ✅ Fuel and damage systems
echo   ✅ Free map data integration
echo   ✅ Educational location learning
echo.

REM Launch Godot with the project
echo Starting Godot Engine...
start "" "Godot.exe" --path "%~dp0"

echo.
echo 🎉 Game launched! Check the Godot window.
echo.
echo 📖 First Time Setup in Godot:
echo 1. Godot will open and scan the project
echo 2. Click "Import & Edit" when prompted
echo 3. Wait for import to complete (first time only)
echo 4. Press F5 or click the Play button to start
echo 5. Select "Main.tscn" as the main scene if asked
echo.
echo 💡 Tips:
echo • Press F5 in Godot to run the game
echo • Use the Scene dock to explore the project
echo • Check the Output panel for debug messages
echo • Modify scripts in the Script editor
echo.
echo 🆓 Everything is FREE and open source!
echo You can modify, distribute, and even sell games made with these tools.
echo.
echo Press any key to close this launcher...
pause >nul
