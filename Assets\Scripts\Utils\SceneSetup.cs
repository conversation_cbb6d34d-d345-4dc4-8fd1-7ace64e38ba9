using UnityEngine;
using UnityEngine.SceneManagement;

namespace DrivingSimulator.Utils
{
    /// <summary>
    /// Utility script to help set up the main scene with all required components
    /// </summary>
    public class SceneSetup : MonoBehaviour
    {
        [Header("Auto Setup Options")]
        [SerializeField] private bool autoSetupOnStart = false;
        [SerializeField] private bool createPlayerCar = true;
        [SerializeField] private bool createGameManager = true;
        [SerializeField] private bool createWorldGenerator = true;
        [SerializeField] private bool createUICanvas = true;
        [SerializeField] private bool createGPSSystem = true;
        [SerializeField] private bool createMapsIntegration = true;
        
        [Header("Car Setup")]
        [SerializeField] private Vector3 carStartPosition = new Vector3(0, 1, 0);
        [SerializeField] private GameObject carModelPrefab;
        
        [Header("Camera Setup")]
        [SerializeField] private Vector3 cameraOffset = new Vector3(0, 1.5f, 0);
        [SerializeField] private bool setupFirstPersonCamera = true;
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupScene();
            }
        }
        
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            Debug.Log("Setting up Driving Simulator scene...");
            
            if (createGameManager)
                CreateGameManager();
            
            if (createMapsIntegration)
                CreateMapsIntegration();
            
            if (createWorldGenerator)
                CreateWorldGenerator();
            
            if (createPlayerCar)
                CreatePlayerCar();
            
            if (createGPSSystem)
                CreateGPSSystem();
            
            if (createUICanvas)
                CreateUICanvas();
            
            SetupLighting();
            SetupGround();
            
            Debug.Log("Scene setup complete!");
        }
        
        private void CreateGameManager()
        {
            if (FindObjectOfType<DrivingSimulator.Managers.GameManager>() != null)
            {
                Debug.Log("GameManager already exists in scene");
                return;
            }
            
            GameObject gameManagerObj = new GameObject("GameManager");
            gameManagerObj.AddComponent<DrivingSimulator.Managers.GameManager>();
            
            Debug.Log("Created GameManager");
        }
        
        private void CreateMapsIntegration()
        {
            if (FindObjectOfType<DrivingSimulator.World.GoogleMapsIntegration>() != null)
            {
                Debug.Log("GoogleMapsIntegration already exists in scene");
                return;
            }
            
            GameObject mapsObj = new GameObject("GoogleMapsIntegration");
            mapsObj.AddComponent<DrivingSimulator.World.GoogleMapsIntegration>();
            
            Debug.Log("Created GoogleMapsIntegration");
        }
        
        private void CreateWorldGenerator()
        {
            if (FindObjectOfType<DrivingSimulator.World.WorldGenerator>() != null)
            {
                Debug.Log("WorldGenerator already exists in scene");
                return;
            }
            
            GameObject worldGenObj = new GameObject("WorldGenerator");
            worldGenObj.AddComponent<DrivingSimulator.World.WorldGenerator>();
            
            Debug.Log("Created WorldGenerator");
        }
        
        private void CreatePlayerCar()
        {
            if (FindObjectOfType<DrivingSimulator.Vehicle.CarController>() != null)
            {
                Debug.Log("PlayerCar already exists in scene");
                return;
            }
            
            GameObject carObj = new GameObject("PlayerCar");
            carObj.transform.position = carStartPosition;
            
            // Add Rigidbody
            Rigidbody rb = carObj.AddComponent<Rigidbody>();
            rb.mass = 1500f; // Typical car mass
            rb.centerOfMass = new Vector3(0, -0.5f, 0.5f);
            
            // Add car controller
            var carController = carObj.AddComponent<DrivingSimulator.Vehicle.CarController>();
            
            // Create car body (simple capsule for now)
            GameObject carBody;
            if (carModelPrefab != null)
            {
                carBody = Instantiate(carModelPrefab, carObj.transform);
            }
            else
            {
                carBody = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                carBody.transform.SetParent(carObj.transform);
                carBody.transform.localPosition = Vector3.zero;
                carBody.transform.localScale = new Vector3(2f, 1f, 4f);
                carBody.name = "CarBody";
                
                // Remove the collider from the visual body (physics handled by wheel colliders)
                Destroy(carBody.GetComponent<CapsuleCollider>());
            }
            
            // Create wheels
            CreateWheels(carObj, carController);
            
            // Setup camera
            if (setupFirstPersonCamera)
            {
                SetupCarCamera(carObj);
            }
            
            Debug.Log("Created PlayerCar with basic setup");
        }
        
        private void CreateWheels(GameObject carObj, DrivingSimulator.Vehicle.CarController carController)
        {
            // Wheel positions (relative to car center)
            Vector3[] wheelPositions = new Vector3[]
            {
                new Vector3(-0.8f, -0.5f, 1.2f),  // Front Left
                new Vector3(0.8f, -0.5f, 1.2f),   // Front Right
                new Vector3(-0.8f, -0.5f, -1.2f), // Rear Left
                new Vector3(0.8f, -0.5f, -1.2f)   // Rear Right
            };
            
            string[] wheelNames = { "FrontLeft", "FrontRight", "RearLeft", "RearRight" };
            
            WheelCollider[] wheelColliders = new WheelCollider[4];
            Transform[] wheelTransforms = new Transform[4];
            
            for (int i = 0; i < 4; i++)
            {
                // Create wheel collider object
                GameObject wheelColliderObj = new GameObject($"WheelCollider_{wheelNames[i]}");
                wheelColliderObj.transform.SetParent(carObj.transform);
                wheelColliderObj.transform.localPosition = wheelPositions[i];
                
                WheelCollider wheelCollider = wheelColliderObj.AddComponent<WheelCollider>();
                wheelCollider.radius = 0.35f;
                wheelCollider.wheelDampingRate = 25f;
                wheelCollider.suspensionDistance = 0.3f;
                wheelCollider.forceAppPointDistance = 0f;
                wheelCollider.mass = 20f;
                
                wheelColliders[i] = wheelCollider;
                
                // Create visual wheel
                GameObject wheelVisual = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                wheelVisual.name = $"Wheel_{wheelNames[i]}";
                wheelVisual.transform.SetParent(carObj.transform);
                wheelVisual.transform.localPosition = wheelPositions[i];
                wheelVisual.transform.localRotation = Quaternion.Euler(0, 0, 90);
                wheelVisual.transform.localScale = new Vector3(0.7f, 0.2f, 0.7f);
                
                // Remove collider from visual wheel
                Destroy(wheelVisual.GetComponent<CapsuleCollider>());
                
                wheelTransforms[i] = wheelVisual.transform;
            }
            
            // Assign wheel colliders to car controller using reflection
            var carControllerType = typeof(DrivingSimulator.Vehicle.CarController);
            var frontLeftField = carControllerType.GetField("frontLeftWheelCollider", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var frontRightField = carControllerType.GetField("frontRightWheelCollider", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var rearLeftField = carControllerType.GetField("rearLeftWheelCollider", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var rearRightField = carControllerType.GetField("rearRightWheelCollider", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            frontLeftField?.SetValue(carController, wheelColliders[0]);
            frontRightField?.SetValue(carController, wheelColliders[1]);
            rearLeftField?.SetValue(carController, wheelColliders[2]);
            rearRightField?.SetValue(carController, wheelColliders[3]);
            
            // Assign wheel transforms
            var frontLeftTransformField = carControllerType.GetField("frontLeftWheelTransform", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var frontRightTransformField = carControllerType.GetField("frontRightWheelTransform", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var rearLeftTransformField = carControllerType.GetField("rearLeftWheelTransform", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var rearRightTransformField = carControllerType.GetField("rearRightWheelTransform", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            frontLeftTransformField?.SetValue(carController, wheelTransforms[0]);
            frontRightTransformField?.SetValue(carController, wheelTransforms[1]);
            rearLeftTransformField?.SetValue(carController, wheelTransforms[2]);
            rearRightTransformField?.SetValue(carController, wheelTransforms[3]);
        }
        
        private void SetupCarCamera(GameObject carObj)
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }
            
            // Position camera inside the car
            mainCamera.transform.SetParent(carObj.transform);
            mainCamera.transform.localPosition = cameraOffset;
            mainCamera.transform.localRotation = Quaternion.identity;
            
            // Add audio listener if not present
            if (mainCamera.GetComponent<AudioListener>() == null)
            {
                mainCamera.gameObject.AddComponent<AudioListener>();
            }
        }
        
        private void CreateGPSSystem()
        {
            if (FindObjectOfType<DrivingSimulator.World.GPSNavigationSystem>() != null)
            {
                Debug.Log("GPSNavigationSystem already exists in scene");
                return;
            }
            
            GameObject gpsObj = new GameObject("GPSNavigationSystem");
            gpsObj.AddComponent<DrivingSimulator.World.GPSNavigationSystem>();
            
            Debug.Log("Created GPSNavigationSystem");
        }
        
        private void CreateUICanvas()
        {
            if (FindObjectOfType<DrivingSimulator.UI.UIManager>() != null)
            {
                Debug.Log("UIManager already exists in scene");
                return;
            }
            
            // Create Canvas
            GameObject canvasObj = new GameObject("UI Canvas");
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            
            canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            // Add UIManager
            canvasObj.AddComponent<DrivingSimulator.UI.UIManager>();
            
            // Create EventSystem if it doesn't exist
            if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
            {
                GameObject eventSystemObj = new GameObject("EventSystem");
                eventSystemObj.AddComponent<UnityEngine.EventSystems.EventSystem>();
                eventSystemObj.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
            }
            
            Debug.Log("Created UI Canvas with UIManager");
        }
        
        private void SetupLighting()
        {
            // Create directional light if none exists
            Light[] lights = FindObjectsOfType<Light>();
            bool hasDirectionalLight = false;
            
            foreach (Light light in lights)
            {
                if (light.type == LightType.Directional)
                {
                    hasDirectionalLight = true;
                    break;
                }
            }
            
            if (!hasDirectionalLight)
            {
                GameObject lightObj = new GameObject("Directional Light");
                Light light = lightObj.AddComponent<Light>();
                light.type = LightType.Directional;
                light.intensity = 1f;
                lightObj.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
                
                Debug.Log("Created Directional Light");
            }
        }
        
        private void SetupGround()
        {
            // Create a simple ground plane if none exists
            GameObject existingGround = GameObject.Find("Ground");
            if (existingGround == null)
            {
                GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
                ground.name = "Ground";
                ground.transform.localScale = new Vector3(100, 1, 100);
                ground.transform.position = Vector3.zero;
                
                // Make it a different color
                Renderer renderer = ground.GetComponent<Renderer>();
                renderer.material.color = new Color(0.3f, 0.5f, 0.3f); // Dark green
                
                Debug.Log("Created Ground plane");
            }
        }
        
        [ContextMenu("Clear Scene")]
        public void ClearScene()
        {
            // Remove all created objects (be careful with this!)
            var gameManager = FindObjectOfType<DrivingSimulator.Managers.GameManager>();
            var carController = FindObjectOfType<DrivingSimulator.Vehicle.CarController>();
            var worldGenerator = FindObjectOfType<DrivingSimulator.World.WorldGenerator>();
            var uiManager = FindObjectOfType<DrivingSimulator.UI.UIManager>();
            var gpsSystem = FindObjectOfType<DrivingSimulator.World.GPSNavigationSystem>();
            var mapsIntegration = FindObjectOfType<DrivingSimulator.World.GoogleMapsIntegration>();
            
            if (gameManager != null) DestroyImmediate(gameManager.gameObject);
            if (carController != null) DestroyImmediate(carController.gameObject);
            if (worldGenerator != null) DestroyImmediate(worldGenerator.gameObject);
            if (uiManager != null) DestroyImmediate(uiManager.gameObject);
            if (gpsSystem != null) DestroyImmediate(gpsSystem.gameObject);
            if (mapsIntegration != null) DestroyImmediate(mapsIntegration.gameObject);
            
            Debug.Log("Scene cleared");
        }
    }
}
