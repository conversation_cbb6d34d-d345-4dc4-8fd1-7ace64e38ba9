<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Driving Complete - Authentic Recreation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #228B22 100%);
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        
        .title {
            font-size: 3em;
            margin-bottom: 20px;
            text-align: center;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            text-align: center;
            color: #ccc;
        }
        
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
            max-width: 800px;
        }
        
        .feature-box {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        button:hover {
            transform: scale(1.05);
        }
        
        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .ui-element {
            position: absolute;
            pointer-events: auto;
            background: rgba(0,0,0,0.8);
            border-radius: 10px;
            padding: 10px;
            border: 2px solid #fff;
            color: white;
        }
        
        #speedometer {
            top: 20px;
            left: 20px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        #controls {
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-btn {
            width: 70px;
            height: 70px;
            border: 3px solid white;
            border-radius: 50%;
            background: rgba(0,0,0,0.7);
            color: white;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s;
        }
        
        .control-btn:active {
            background: rgba(255,255,255,0.3);
            transform: scale(0.95);
        }
        
        #gameInfo {
            top: 20px;
            right: 20px;
            min-width: 200px;
        }
        
        #missionPanel {
            bottom: 20px;
            right: 20px;
            max-width: 250px;
        }
        
        .hidden { display: none !important; }
        
        @media (max-width: 768px) {
            .features {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2em;
            }
            
            #controls {
                bottom: 10px;
                gap: 10px;
            }
            
            .control-btn {
                width: 60px;
                height: 60px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- Start Screen -->
        <div id="startScreen">
            <h1 class="title">🚗 Dr. Driving Complete</h1>
            <p class="subtitle">Authentic Mobile Driving Experience Recreation</p>
            
            <div class="features">
                <div class="feature-box">
                    <div class="feature-icon">🛣️</div>
                    <h3>Highway Mode</h3>
                    <p>Drive on highways with traffic</p>
                </div>
                <div class="feature-box">
                    <div class="feature-icon">🏙️</div>
                    <h3>City Mode</h3>
                    <p>Navigate through city streets</p>
                </div>
                <div class="feature-box">
                    <div class="feature-icon">🅿️</div>
                    <h3>Parking Mode</h3>
                    <p>Precision parking challenges</p>
                </div>
                <div class="feature-box">
                    <div class="feature-icon">🌙</div>
                    <h3>Night Mode</h3>
                    <p>Low visibility driving</p>
                </div>
                <div class="feature-box">
                    <div class="feature-icon">📱</div>
                    <h3>Mobile Controls</h3>
                    <p>Touch steering and pedals</p>
                </div>
                <div class="feature-box">
                    <div class="feature-icon">🎯</div>
                    <h3>Missions</h3>
                    <p>Delivery and parking tasks</p>
                </div>
            </div>
            
            <div>
                <button onclick="startGame('highway')">🛣️ Highway Mode</button>
                <button onclick="startGame('city')">🏙️ City Mode</button>
                <button onclick="startGame('parking')">🅿️ Parking Mode</button>
                <button onclick="showInstructions()">📖 Instructions</button>
            </div>
            
            <p style="margin-top: 20px; font-size: 0.9em; opacity: 0.7;">
                Complete Dr. Driving recreation - All features included!
            </p>
        </div>
        
        <!-- Game Canvas -->
        <canvas id="gameCanvas"></canvas>
        
        <!-- Game UI -->
        <div id="ui" class="hidden">
            <!-- Speedometer -->
            <div id="speedometer" class="ui-element">
                <div style="font-size: 24px; font-weight: bold; color: #00ff00;" id="speedValue">0</div>
                <div style="font-size: 12px;">KM/H</div>
            </div>
            
            <!-- Game Info -->
            <div id="gameInfo" class="ui-element">
                <div>Mode: <span id="gameMode">Highway</span></div>
                <div>Fuel: <span id="fuel">100</span>%</div>
                <div>Score: <span id="score">0</span></div>
                <div>Time: <span id="timer">0:00</span></div>
            </div>
            
            <!-- Mission Panel -->
            <div id="missionPanel" class="ui-element">
                <h4>🎯 Current Mission</h4>
                <div id="missionText">Drive safely and follow traffic rules</div>
                <div>Progress: <span id="progress">0</span>%</div>
            </div>
            
            <!-- Controls -->
            <div id="controls">
                <div class="control-btn" id="leftBtn">←</div>
                <div class="control-btn" id="gasBtn">⬆</div>
                <div class="control-btn" id="brakeBtn">⬇</div>
                <div class="control-btn" id="rightBtn">→</div>
                <div class="control-btn" id="hornBtn">🔊</div>
                <div class="control-btn" id="lightBtn">💡</div>
            </div>
        </div>
    </div>

    <script>
        // Dr. Driving Complete Game Engine
        class DrDrivingGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.gameMode = 'highway';
                this.gameStarted = false;
                this.startTime = 0;
                
                // Car properties (Dr. Driving style)
                this.car = {
                    x: 0,
                    y: 0,
                    angle: 0,
                    speed: 0,
                    maxSpeed: 120,
                    width: 30,
                    height: 60,
                    fuel: 100,
                    damage: 0
                };
                
                // Camera (3D perspective)
                this.camera = {
                    x: 0,
                    y: 0,
                    followDistance: 100
                };
                
                // Game world
                this.world = {
                    roads: [],
                    buildings: [],
                    traffic: [],
                    missions: []
                };
                
                // Input
                this.keys = {};
                this.touch = {
                    left: false,
                    right: false,
                    gas: false,
                    brake: false
                };
                
                // Game stats
                this.score = 0;
                this.missionProgress = 0;
                
                this.init();
            }
            
            init() {
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());
                this.setupControls();
                this.generateWorld();
                
                console.log('🚗 Dr. Driving Complete initialized');
            }
            
            resizeCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }
            
            setupControls() {
                // Keyboard
                document.addEventListener('keydown', (e) => {
                    this.keys[e.code] = true;
                    e.preventDefault();
                });
                
                document.addEventListener('keyup', (e) => {
                    this.keys[e.code] = false;
                    e.preventDefault();
                });
                
                // Touch controls
                const setupButton = (id, property) => {
                    const btn = document.getElementById(id);
                    btn.addEventListener('touchstart', (e) => {
                        this.touch[property] = true;
                        e.preventDefault();
                    });
                    btn.addEventListener('touchend', (e) => {
                        this.touch[property] = false;
                        e.preventDefault();
                    });
                    btn.addEventListener('mousedown', (e) => {
                        this.touch[property] = true;
                        e.preventDefault();
                    });
                    btn.addEventListener('mouseup', (e) => {
                        this.touch[property] = false;
                        e.preventDefault();
                    });
                };
                
                setupButton('leftBtn', 'left');
                setupButton('rightBtn', 'right');
                setupButton('gasBtn', 'gas');
                setupButton('brakeBtn', 'brake');
                
                // Special buttons
                document.getElementById('hornBtn').addEventListener('click', () => {
                    console.log('🔊 Horn!');
                });
                
                document.getElementById('lightBtn').addEventListener('click', () => {
                    console.log('💡 Lights toggled!');
                });
            }
            
            generateWorld() {
                // Generate roads based on mode
                switch (this.gameMode) {
                    case 'highway':
                        this.generateHighway();
                        break;
                    case 'city':
                        this.generateCity();
                        break;
                    case 'parking':
                        this.generateParkingLot();
                        break;
                }
                
                // Generate traffic
                this.generateTraffic();
                
                // Generate buildings
                this.generateBuildings();
            }
            
            generateHighway() {
                // Highway with multiple lanes
                for (let i = -2000; i <= 2000; i += 100) {
                    this.world.roads.push({
                        x: -100, y: i, width: 200, height: 50,
                        type: 'highway'
                    });
                }
            }
            
            generateCity() {
                // City grid
                for (let x = -1000; x <= 1000; x += 200) {
                    for (let y = -1000; y <= 1000; y += 200) {
                        this.world.roads.push({
                            x: x - 25, y: y - 25, width: 50, height: 50,
                            type: 'intersection'
                        });
                    }
                }
            }
            
            generateParkingLot() {
                // Parking spaces
                for (let i = 0; i < 20; i++) {
                    this.world.roads.push({
                        x: (i % 5) * 80 - 160,
                        y: Math.floor(i / 5) * 100 - 150,
                        width: 60, height: 80,
                        type: 'parking'
                    });
                }
            }
            
            generateTraffic() {
                // AI traffic cars
                for (let i = 0; i < 10; i++) {
                    this.world.traffic.push({
                        x: Math.random() * 400 - 200,
                        y: Math.random() * 400 - 200,
                        angle: Math.random() * Math.PI * 2,
                        speed: 20 + Math.random() * 40,
                        color: `hsl(${Math.random() * 360}, 70%, 50%)`
                    });
                }
            }
            
            generateBuildings() {
                // Buildings around roads
                for (let i = 0; i < 30; i++) {
                    this.world.buildings.push({
                        x: Math.random() * 800 - 400,
                        y: Math.random() * 800 - 400,
                        width: 40 + Math.random() * 60,
                        height: 40 + Math.random() * 60,
                        color: `hsl(${Math.random() * 60 + 200}, 50%, ${30 + Math.random() * 20}%)`
                    });
                }
            }
            
            start(mode) {
                this.gameMode = mode;
                this.gameStarted = true;
                this.startTime = Date.now();
                
                // Reset car position
                this.car.x = 0;
                this.car.y = 0;
                this.car.angle = 0;
                this.car.speed = 0;
                this.car.fuel = 100;
                this.car.damage = 0;
                
                // Generate world for mode
                this.generateWorld();
                
                // Show game UI
                document.getElementById('startScreen').style.display = 'none';
                document.getElementById('ui').classList.remove('hidden');
                document.getElementById('gameMode').textContent = mode.charAt(0).toUpperCase() + mode.slice(1);
                
                // Start game loop
                this.gameLoop();
                
                console.log(`🚗 Dr. Driving ${mode} mode started`);
            }
            
            gameLoop() {
                if (!this.gameStarted) return;
                
                this.update();
                this.render();
                this.updateUI();
                
                requestAnimationFrame(() => this.gameLoop());
            }
            
            update() {
                // Handle input
                let acceleration = 0;
                let steering = 0;
                
                // Keyboard
                if (this.keys['KeyW'] || this.keys['ArrowUp']) acceleration = 1;
                if (this.keys['KeyS'] || this.keys['ArrowDown']) acceleration = -0.5;
                if (this.keys['KeyA'] || this.keys['ArrowLeft']) steering = -1;
                if (this.keys['KeyD'] || this.keys['ArrowRight']) steering = 1;
                
                // Touch
                if (this.touch.gas) acceleration = 1;
                if (this.touch.brake) acceleration = -0.5;
                if (this.touch.left) steering = -1;
                if (this.touch.right) steering = 1;
                
                // Car physics
                this.car.speed += acceleration * 0.5;
                this.car.speed *= 0.95; // Friction
                this.car.speed = Math.max(-this.car.maxSpeed/3, Math.min(this.car.maxSpeed, this.car.speed));
                
                // Steering
                if (Math.abs(this.car.speed) > 1) {
                    this.car.angle += steering * 0.03 * (this.car.speed / this.car.maxSpeed);
                }
                
                // Movement
                this.car.x += Math.cos(this.car.angle) * this.car.speed * 0.5;
                this.car.y += Math.sin(this.car.angle) * this.car.speed * 0.5;
                
                // Camera follow
                this.camera.x = this.car.x - Math.cos(this.car.angle) * this.camera.followDistance;
                this.camera.y = this.car.y - Math.sin(this.car.angle) * this.camera.followDistance;
                
                // Fuel consumption
                if (acceleration > 0) {
                    this.car.fuel -= 0.02;
                    this.car.fuel = Math.max(0, this.car.fuel);
                }
                
                // Update traffic
                this.world.traffic.forEach(traffic => {
                    traffic.x += Math.cos(traffic.angle) * traffic.speed * 0.01;
                    traffic.y += Math.sin(traffic.angle) * traffic.speed * 0.01;
                });
                
                // Update score
                if (this.car.speed > 0) {
                    this.score += Math.floor(this.car.speed * 0.1);
                    this.missionProgress = Math.min(100, this.missionProgress + 0.1);
                }
            }
            
            render() {
                const ctx = this.ctx;
                const canvas = this.canvas;
                
                // Clear
                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Save context for camera
                ctx.save();
                ctx.translate(canvas.width / 2, canvas.height / 2);
                ctx.rotate(-this.car.angle);
                ctx.translate(-this.car.x, -this.car.y);
                
                // Draw ground
                ctx.fillStyle = '#228B22';
                ctx.fillRect(-2000, -2000, 4000, 4000);
                
                // Draw roads
                ctx.fillStyle = '#444444';
                this.world.roads.forEach(road => {
                    ctx.fillRect(road.x, road.y, road.width, road.height);
                    
                    // Road markings
                    if (road.type === 'highway') {
                        ctx.strokeStyle = '#ffff00';
                        ctx.lineWidth = 2;
                        ctx.setLineDash([10, 10]);
                        ctx.beginPath();
                        ctx.moveTo(road.x + road.width/2, road.y);
                        ctx.lineTo(road.x + road.width/2, road.y + road.height);
                        ctx.stroke();
                        ctx.setLineDash([]);
                    }
                });
                
                // Draw buildings
                this.world.buildings.forEach(building => {
                    // Shadow
                    ctx.fillStyle = 'rgba(0,0,0,0.3)';
                    ctx.fillRect(building.x + 3, building.y + 3, building.width, building.height);
                    
                    // Building
                    ctx.fillStyle = building.color;
                    ctx.fillRect(building.x, building.y, building.width, building.height);
                    
                    // Outline
                    ctx.strokeStyle = '#000';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(building.x, building.y, building.width, building.height);
                });
                
                // Draw traffic
                this.world.traffic.forEach(traffic => {
                    ctx.save();
                    ctx.translate(traffic.x, traffic.y);
                    ctx.rotate(traffic.angle);
                    
                    ctx.fillStyle = traffic.color;
                    ctx.fillRect(-15, -8, 30, 16);
                    
                    ctx.fillStyle = '#87CEEB';
                    ctx.fillRect(-12, -6, 24, 12);
                    
                    ctx.restore();
                });
                
                // Draw player car
                ctx.save();
                ctx.translate(this.car.x, this.car.y);
                ctx.rotate(this.car.angle);
                
                // Car shadow
                ctx.fillStyle = 'rgba(0,0,0,0.3)';
                ctx.fillRect(-this.car.width/2 + 2, -this.car.height/2 + 2, this.car.width, this.car.height);
                
                // Car body
                ctx.fillStyle = '#ff4444';
                ctx.fillRect(-this.car.width/2, -this.car.height/2, this.car.width, this.car.height);
                
                // Car windows
                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(-this.car.width/2 + 3, -this.car.height/2 + 5, this.car.width - 6, 15);
                ctx.fillRect(-this.car.width/2 + 3, this.car.height/2 - 20, this.car.width - 6, 15);
                
                // Wheels
                ctx.fillStyle = '#000';
                ctx.fillRect(-this.car.width/2 - 3, -this.car.height/2 + 8, 6, 8);
                ctx.fillRect(this.car.width/2 - 3, -this.car.height/2 + 8, 6, 8);
                ctx.fillRect(-this.car.width/2 - 3, this.car.height/2 - 16, 6, 8);
                ctx.fillRect(this.car.width/2 - 3, this.car.height/2 - 16, 6, 8);
                
                ctx.restore();
                ctx.restore();
            }
            
            updateUI() {
                document.getElementById('speedValue').textContent = Math.floor(Math.abs(this.car.speed * 8));
                document.getElementById('fuel').textContent = Math.floor(this.car.fuel);
                document.getElementById('score').textContent = this.score;
                document.getElementById('progress').textContent = Math.floor(this.missionProgress);
                
                // Timer
                const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('timer').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }
        
        // Global game instance
        let game = new DrDrivingGame();
        
        // Global functions
        function startGame(mode) {
            game.start(mode);
        }
        
        function showInstructions() {
            alert(`🚗 Dr. Driving Complete - Instructions

🎮 DESKTOP CONTROLS:
• W/↑ - Accelerate
• S/↓ - Brake
• A/← - Turn Left
• D/→ - Turn Right

📱 MOBILE CONTROLS:
• ⬆ Button - Gas pedal
• ⬇ Button - Brake pedal
• ← → Buttons - Steering
• 🔊 Button - Horn
• 💡 Button - Headlights

🎯 GAME MODES:
• Highway Mode - Drive on highways with traffic
• City Mode - Navigate city streets and intersections
• Parking Mode - Precision parking challenges

🏆 OBJECTIVES:
• Follow traffic rules
• Complete missions safely
• Manage fuel consumption
• Avoid collisions
• Park accurately

🚗 AUTHENTIC DR. DRIVING FEATURES:
• Realistic car physics
• 3D perspective driving
• Mobile-optimized controls
• Traffic AI system
• Fuel management
• Mission-based gameplay

Drive safely and enjoy the authentic Dr. Driving experience!`);
        }
        
        console.log('🎮 Dr. Driving Complete loaded successfully!');
    </script>
</body>
</html>
