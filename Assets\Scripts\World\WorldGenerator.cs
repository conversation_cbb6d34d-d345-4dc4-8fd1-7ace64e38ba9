using UnityEngine;
using System.Collections.Generic;

namespace DrivingSimulator.World
{
    /// <summary>
    /// Generates the 3D world based on Google Maps data
    /// </summary>
    public class WorldGenerator : MonoBehaviour
    {
        [Header("Generation Settings")]
        [SerializeField] private float worldScale = 1f;
        [SerializeField] private int chunkSize = 1000; // Size of each world chunk
        [SerializeField] private int loadDistance = 2000; // Distance to load chunks
        [SerializeField] private int unloadDistance = 3000; // Distance to unload chunks
        
        [Header("Road Generation")]
        [SerializeField] private GameObject roadPrefab;
        [SerializeField] private GameObject intersectionPrefab;
        [SerializeField] private Material roadMaterial;
        [SerializeField] private float roadWidth = 8f;
        [SerializeField] private float roadHeight = 0.1f;
        
        [Header("Building Generation")]
        [SerializeField] private GameObject[] residentialBuildingPrefabs;
        [SerializeField] private GameObject[] commercialBuildingPrefabs;
        [SerializeField] private GameObject[] industrialBuildingPrefabs;
        [SerializeField] private float buildingSpacing = 50f;
        
        [Header("POI Generation")]
        [SerializeField] private GameObject gasStationPrefab;
        [SerializeField] private GameObject repairShopPrefab;
        [SerializeField] private GameObject restaurantPrefab;
        [SerializeField] private GameObject hospitalPrefab;
        [SerializeField] private GameObject bankPrefab;
        
        [Header("Environment")]
        [SerializeField] private GameObject treePrefab;
        [SerializeField] private GameObject streetLightPrefab;
        [SerializeField] private GameObject trafficLightPrefab;
        [SerializeField] private Material groundMaterial;
        
        // World data
        private Dictionary<Vector2Int, WorldChunk> loadedChunks = new Dictionary<Vector2Int, WorldChunk>();
        private Transform playerTransform;
        private GoogleMapsIntegration mapsIntegration;
        
        // Generated objects
        private Transform roadsParent;
        private Transform buildingsParent;
        private Transform poisParent;
        private Transform environmentParent;
        
        [System.Serializable]
        public class WorldChunk
        {
            public Vector2Int coordinates;
            public GameObject chunkObject;
            public List<GameObject> roads = new List<GameObject>();
            public List<GameObject> buildings = new List<GameObject>();
            public List<GameObject> pois = new List<GameObject>();
            public List<GameObject> environment = new List<GameObject>();
            public bool isLoaded = false;
        }
        
        private void Start()
        {
            InitializeWorldGenerator();
        }
        
        private void Update()
        {
            if (playerTransform != null)
            {
                UpdateWorldChunks();
            }
        }
        
        private void InitializeWorldGenerator()
        {
            // Get references
            var playerCar = FindObjectOfType<DrivingSimulator.Vehicle.CarController>();
            if (playerCar != null)
                playerTransform = playerCar.transform;
            
            mapsIntegration = FindObjectOfType<GoogleMapsIntegration>();
            
            // Create parent objects for organization
            CreateParentObjects();
            
            // Subscribe to map data events
            if (mapsIntegration != null)
            {
                mapsIntegration.OnRoadsLoaded += OnRoadsLoaded;
                mapsIntegration.OnBuildingsLoaded += OnBuildingsLoaded;
                mapsIntegration.OnPOIsLoaded += OnPOIsLoaded;
            }
            
            // Generate initial ground plane
            GenerateGroundPlane();
        }
        
        private void CreateParentObjects()
        {
            GameObject worldParent = new GameObject("Generated World");
            
            roadsParent = new GameObject("Roads").transform;
            roadsParent.SetParent(worldParent.transform);
            
            buildingsParent = new GameObject("Buildings").transform;
            buildingsParent.SetParent(worldParent.transform);
            
            poisParent = new GameObject("Points of Interest").transform;
            poisParent.SetParent(worldParent.transform);
            
            environmentParent = new GameObject("Environment").transform;
            environmentParent.SetParent(worldParent.transform);
        }
        
        private void GenerateGroundPlane()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.localScale = new Vector3(1000, 1, 1000); // Large ground plane
            ground.transform.position = Vector3.zero;
            
            if (groundMaterial != null)
            {
                ground.GetComponent<Renderer>().material = groundMaterial;
            }
            
            ground.transform.SetParent(environmentParent);
        }
        
        private void UpdateWorldChunks()
        {
            Vector2Int playerChunk = GetChunkCoordinates(playerTransform.position);
            
            // Load chunks around player
            for (int x = -2; x <= 2; x++)
            {
                for (int z = -2; z <= 2; z++)
                {
                    Vector2Int chunkCoord = playerChunk + new Vector2Int(x, z);
                    
                    if (!loadedChunks.ContainsKey(chunkCoord))
                    {
                        LoadChunk(chunkCoord);
                    }
                }
            }
            
            // Unload distant chunks
            List<Vector2Int> chunksToUnload = new List<Vector2Int>();
            foreach (var chunk in loadedChunks)
            {
                float distance = Vector2.Distance(chunk.Key, playerChunk);
                if (distance > 4) // Unload chunks more than 4 chunks away
                {
                    chunksToUnload.Add(chunk.Key);
                }
            }
            
            foreach (var chunkCoord in chunksToUnload)
            {
                UnloadChunk(chunkCoord);
            }
        }
        
        private Vector2Int GetChunkCoordinates(Vector3 worldPosition)
        {
            int x = Mathf.FloorToInt(worldPosition.x / chunkSize);
            int z = Mathf.FloorToInt(worldPosition.z / chunkSize);
            return new Vector2Int(x, z);
        }
        
        private void LoadChunk(Vector2Int chunkCoord)
        {
            WorldChunk chunk = new WorldChunk
            {
                coordinates = chunkCoord,
                chunkObject = new GameObject($"Chunk_{chunkCoord.x}_{chunkCoord.y}")
            };
            
            Vector3 chunkWorldPos = new Vector3(chunkCoord.x * chunkSize, 0, chunkCoord.y * chunkSize);
            chunk.chunkObject.transform.position = chunkWorldPos;
            
            // Generate chunk content
            GenerateChunkRoads(chunk);
            GenerateChunkBuildings(chunk);
            GenerateChunkEnvironment(chunk);
            
            chunk.isLoaded = true;
            loadedChunks[chunkCoord] = chunk;
            
            Debug.Log($"Loaded chunk {chunkCoord}");
        }
        
        private void UnloadChunk(Vector2Int chunkCoord)
        {
            if (loadedChunks.TryGetValue(chunkCoord, out WorldChunk chunk))
            {
                if (chunk.chunkObject != null)
                {
                    Destroy(chunk.chunkObject);
                }
                
                loadedChunks.Remove(chunkCoord);
                Debug.Log($"Unloaded chunk {chunkCoord}");
            }
        }
        
        private void GenerateChunkRoads(WorldChunk chunk)
        {
            // Generate a simple road network for this chunk
            Vector3 chunkCenter = chunk.chunkObject.transform.position;
            
            // Create main roads (simplified grid)
            CreateRoad(chunkCenter + new Vector3(-chunkSize/2, 0, 0), 
                      chunkCenter + new Vector3(chunkSize/2, 0, 0), chunk);
            
            CreateRoad(chunkCenter + new Vector3(0, 0, -chunkSize/2), 
                      chunkCenter + new Vector3(0, 0, chunkSize/2), chunk);
            
            // Add some secondary roads
            for (int i = 0; i < 3; i++)
            {
                Vector3 start = chunkCenter + new Vector3(
                    Random.Range(-chunkSize/2, chunkSize/2), 0, -chunkSize/2);
                Vector3 end = chunkCenter + new Vector3(
                    Random.Range(-chunkSize/2, chunkSize/2), 0, chunkSize/2);
                
                CreateRoad(start, end, chunk);
            }
        }
        
        private void CreateRoad(Vector3 start, Vector3 end, WorldChunk chunk)
        {
            GameObject road = new GameObject("Road");
            road.transform.SetParent(chunk.chunkObject.transform);
            
            // Create road mesh
            MeshRenderer renderer = road.AddComponent<MeshRenderer>();
            MeshFilter filter = road.AddComponent<MeshFilter>();
            
            if (roadMaterial != null)
                renderer.material = roadMaterial;
            
            // Generate road mesh
            Mesh roadMesh = GenerateRoadMesh(start, end);
            filter.mesh = roadMesh;
            
            // Add collider
            MeshCollider collider = road.AddComponent<MeshCollider>();
            collider.sharedMesh = roadMesh;
            
            chunk.roads.Add(road);
        }
        
        private Mesh GenerateRoadMesh(Vector3 start, Vector3 end)
        {
            Mesh mesh = new Mesh();
            
            Vector3 direction = (end - start).normalized;
            Vector3 perpendicular = Vector3.Cross(direction, Vector3.up) * roadWidth * 0.5f;
            
            Vector3[] vertices = new Vector3[4]
            {
                start - perpendicular,
                start + perpendicular,
                end + perpendicular,
                end - perpendicular
            };
            
            int[] triangles = new int[6] { 0, 1, 2, 0, 2, 3 };
            
            Vector2[] uvs = new Vector2[4]
            {
                new Vector2(0, 0),
                new Vector2(1, 0),
                new Vector2(1, 1),
                new Vector2(0, 1)
            };
            
            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.uv = uvs;
            mesh.RecalculateNormals();
            
            return mesh;
        }
        
        private void GenerateChunkBuildings(WorldChunk chunk)
        {
            Vector3 chunkCenter = chunk.chunkObject.transform.position;
            
            // Generate buildings in a grid pattern with some randomness
            for (int x = -2; x <= 2; x++)
            {
                for (int z = -2; z <= 2; z++)
                {
                    if (Random.value > 0.3f) // 70% chance to place a building
                    {
                        Vector3 buildingPos = chunkCenter + new Vector3(
                            x * buildingSpacing + Random.Range(-20f, 20f),
                            0,
                            z * buildingSpacing + Random.Range(-20f, 20f)
                        );
                        
                        CreateBuilding(buildingPos, chunk);
                    }
                }
            }
        }
        
        private void CreateBuilding(Vector3 position, WorldChunk chunk)
        {
            GameObject[] prefabArray = residentialBuildingPrefabs;
            
            // Choose building type based on location
            float distanceFromCenter = Vector3.Distance(position, Vector3.zero);
            if (distanceFromCenter < 500f)
                prefabArray = commercialBuildingPrefabs;
            else if (distanceFromCenter > 1500f)
                prefabArray = industrialBuildingPrefabs;
            
            if (prefabArray != null && prefabArray.Length > 0)
            {
                GameObject prefab = prefabArray[Random.Range(0, prefabArray.Length)];
                if (prefab != null)
                {
                    GameObject building = Instantiate(prefab, position, 
                        Quaternion.Euler(0, Random.Range(0, 360), 0));
                    building.transform.SetParent(chunk.chunkObject.transform);
                    chunk.buildings.Add(building);
                }
            }
            else
            {
                // Create a simple procedural building
                CreateProceduralBuilding(position, chunk);
            }
        }
        
        private void CreateProceduralBuilding(Vector3 position, WorldChunk chunk)
        {
            GameObject building = GameObject.CreatePrimitive(PrimitiveType.Cube);
            building.name = "Procedural Building";
            building.transform.position = position;
            
            // Random building dimensions
            float width = Random.Range(10f, 30f);
            float height = Random.Range(10f, 50f);
            float depth = Random.Range(10f, 30f);
            
            building.transform.localScale = new Vector3(width, height, depth);
            building.transform.position += Vector3.up * height * 0.5f; // Lift to ground level
            
            // Random color
            Renderer renderer = building.GetComponent<Renderer>();
            renderer.material.color = new Color(
                Random.Range(0.5f, 1f),
                Random.Range(0.5f, 1f),
                Random.Range(0.5f, 1f)
            );
            
            building.transform.SetParent(chunk.chunkObject.transform);
            chunk.buildings.Add(building);
        }
        
        private void GenerateChunkEnvironment(WorldChunk chunk)
        {
            Vector3 chunkCenter = chunk.chunkObject.transform.position;
            
            // Add some trees and street lights
            for (int i = 0; i < 10; i++)
            {
                Vector3 pos = chunkCenter + new Vector3(
                    Random.Range(-chunkSize/2, chunkSize/2),
                    0,
                    Random.Range(-chunkSize/2, chunkSize/2)
                );
                
                if (Random.value > 0.5f && treePrefab != null)
                {
                    GameObject tree = Instantiate(treePrefab, pos, Quaternion.identity);
                    tree.transform.SetParent(chunk.chunkObject.transform);
                    chunk.environment.Add(tree);
                }
                else if (streetLightPrefab != null)
                {
                    GameObject light = Instantiate(streetLightPrefab, pos, Quaternion.identity);
                    light.transform.SetParent(chunk.chunkObject.transform);
                    chunk.environment.Add(light);
                }
            }
        }
        
        // Event handlers for Google Maps data
        private void OnRoadsLoaded(List<GoogleMapsIntegration.RoadData> roads)
        {
            Debug.Log($"Received {roads.Count} roads from Google Maps");
            // In a full implementation, use this data to generate accurate roads
        }
        
        private void OnBuildingsLoaded(List<GoogleMapsIntegration.BuildingData> buildings)
        {
            Debug.Log($"Received {buildings.Count} buildings from Google Maps");
            // In a full implementation, use this data to place accurate buildings
        }
        
        private void OnPOIsLoaded(List<GoogleMapsIntegration.POIData> pois)
        {
            Debug.Log($"Received {pois.Count} POIs from Google Maps");
            
            // Place POIs in the world
            foreach (var poi in pois)
            {
                CreatePOI(poi);
            }
        }
        
        private void CreatePOI(GoogleMapsIntegration.POIData poi)
        {
            GameObject prefab = null;
            
            switch (poi.type)
            {
                case "gas_station":
                    prefab = gasStationPrefab;
                    break;
                case "car_repair":
                    prefab = repairShopPrefab;
                    break;
                case "restaurant":
                    prefab = restaurantPrefab;
                    break;
                case "hospital":
                    prefab = hospitalPrefab;
                    break;
                case "bank":
                    prefab = bankPrefab;
                    break;
            }
            
            if (prefab != null)
            {
                GameObject poiObject = Instantiate(prefab, poi.position, Quaternion.identity);
                poiObject.name = poi.name;
                poiObject.transform.SetParent(poisParent);
                
                // Add POI component for interaction
                POIInteraction poiInteraction = poiObject.AddComponent<POIInteraction>();
                poiInteraction.Initialize(poi);
            }
        }
    }
}
