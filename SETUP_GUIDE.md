# Realistic Driving Simulator - Setup Guide

This guide will help you set up and run the Realistic Driving Simulator project.

## 📋 Prerequisites

### Required Software
1. **Unity Hub** (Latest version)
   - Download from: https://unity3d.com/get-unity/download
   
2. **Unity Editor 2023.3.0f1 LTS** (or later)
   - Install through Unity Hub
   - Make sure to include:
     - Windows Build Support (IL2CPP)
     - Visual Studio Community (or your preferred IDE)

3. **Visual Studio 2022** or **Visual Studio Code**
   - For C# development and debugging

4. **Git** (for version control)
   - Download from: https://git-scm.com/

### API Keys Required
1. **Google Maps API Key**
   - Go to: https://console.cloud.google.com/
   - Create a new project or select existing
   - Enable the following APIs:
     - Maps JavaScript API
     - Directions API
     - Places API
     - Roads API (optional)
   - Create credentials (API Key)
   - Restrict the key to your domain/IP for security

2. **Google Places API Key** (can be the same as Maps API)

## 🚀 Installation Steps

### 1. Clone/Download the Project
```bash
git clone <repository-url>
cd realistic-driving-simulator
```

### 2. Open in Unity
1. Open Unity Hub
2. Click "Add" and select the project folder
3. Click on the project to open it in Unity

### 3. Configure API Keys
1. In Unity, navigate to the scene containing `GoogleMapsIntegration`
2. Select the GameObject with the `GoogleMapsIntegration` script
3. In the Inspector, enter your API keys:
   - **Google Maps Api Key**: Your Google Maps API key
   - **Places Api Key**: Your Google Places API key (can be same as Maps API)

### 4. Install Dependencies
The project uses the following Unity packages (should auto-install):
- TextMeshPro
- Universal Render Pipeline (URP)
- Input System
- Post Processing

If packages don't install automatically:
1. Open Window → Package Manager
2. Search for and install the required packages

### 5. Configure Input System
1. Go to Edit → Project Settings → XR Plug-in Management → Input System Package
2. Set "Active Input Handling" to "Input System Package (New)"
3. Restart Unity when prompted

## 🎮 Controls

### Driving Controls
- **W/A/S/D** or **Arrow Keys**: Accelerate/Brake/Steer
- **Space**: Handbrake
- **Escape**: Pause menu
- **E**: Interact with POIs (gas stations, repair shops, etc.)

### Camera Controls
- **Mouse**: Look around (first-person view)
- **Mouse Scroll**: Zoom in/out (if applicable)

## 🗺️ Setting Up Google Maps Integration

### 1. Enable Required APIs
In Google Cloud Console, enable:
```
- Maps JavaScript API
- Directions API
- Places API (New)
- Geocoding API
- Roads API (optional)
```

### 2. API Key Configuration
```csharp
// In GoogleMapsIntegration.cs, set your API keys:
[SerializeField] private string googleMapsApiKey = "YOUR_GOOGLE_MAPS_API_KEY";
[SerializeField] private string placesApiKey = "YOUR_PLACES_API_KEY";
```

### 3. API Usage Limits
- Monitor your API usage in Google Cloud Console
- Set up billing alerts to avoid unexpected charges
- Consider implementing caching for frequently requested data

## 🏗️ Project Structure

```
Assets/
├── Scripts/
│   ├── Vehicle/          # Car physics and controls
│   │   └── CarController.cs
│   ├── World/           # Map generation and world systems
│   │   ├── GoogleMapsIntegration.cs
│   │   ├── WorldGenerator.cs
│   │   ├── GPSNavigationSystem.cs
│   │   └── POIInteraction.cs
│   ├── UI/              # User interface components
│   │   └── UIManager.cs
│   ├── Managers/        # Game state and system managers
│   │   └── GameManager.cs
│   └── Utils/           # Utility scripts and helpers
├── Prefabs/             # Reusable game objects
├── Materials/           # Visual materials and shaders
├── Scenes/              # Game scenes
└── Resources/           # Runtime-loaded assets
```

## 🎯 First Run

### 1. Create the Main Scene
1. Create a new scene: File → New Scene
2. Save it as "MainScene" in Assets/Scenes/

### 2. Set Up Basic Objects
1. **Create Player Car**:
   - Create an empty GameObject named "PlayerCar"
   - Add the `CarController` script
   - Add a car model (or use a simple capsule for testing)
   - Set up wheel colliders and transforms

2. **Create Game Manager**:
   - Create an empty GameObject named "GameManager"
   - Add the `GameManager` script

3. **Create World Generator**:
   - Create an empty GameObject named "WorldGenerator"
   - Add the `WorldGenerator` script

4. **Create UI Canvas**:
   - Create a Canvas (UI → Canvas)
   - Add the `UIManager` script
   - Set up basic UI elements

### 3. Configure Scene
1. Set up lighting (Window → Rendering → Lighting)
2. Configure camera for first-person view
3. Add a ground plane for testing

## 🔧 Troubleshooting

### Common Issues

#### 1. API Key Not Working
- Verify the API key is correct
- Check that required APIs are enabled
- Ensure billing is set up in Google Cloud Console
- Check API key restrictions

#### 2. Car Physics Issues
- Verify WheelCollider setup
- Check that wheel transforms are assigned
- Adjust suspension and friction settings
- Ensure the car has a Rigidbody component

#### 3. UI Not Responding
- Check that EventSystem is present in scene
- Verify Canvas settings (Screen Space - Overlay)
- Ensure UI elements have proper anchoring

#### 4. Performance Issues
- Reduce world generation distance
- Optimize building/road generation
- Use object pooling for frequently spawned objects
- Profile using Unity Profiler (Window → Analysis → Profiler)

### Debug Mode
Enable debug logging by adding this to any script:
```csharp
Debug.Log("Your debug message here");
```

View logs in Unity Console (Window → General → Console)

## 📱 Building the Game

### Windows Build
1. Go to File → Build Settings
2. Select "PC, Mac & Linux Standalone"
3. Set Target Platform to "Windows"
4. Click "Build" and choose output folder

### Build Settings
- **Compression Method**: LZ4HC (faster loading)
- **Development Build**: Check for debugging
- **Script Debugging**: Check if needed

## 🔄 Updates and Maintenance

### Updating Google Maps Data
- The system automatically loads new data based on player location
- Clear cache if experiencing issues with outdated data

### Performance Optimization
- Monitor frame rate and memory usage
- Adjust world generation settings based on target hardware
- Use Unity Profiler to identify bottlenecks

## 📞 Support

### Getting Help
1. Check Unity Console for error messages
2. Review this setup guide
3. Check Unity documentation for specific components
4. Google Cloud Console for API-related issues

### Useful Resources
- Unity Documentation: https://docs.unity3d.com/
- Google Maps Platform: https://developers.google.com/maps
- Unity Learn: https://learn.unity.com/

## 🎉 You're Ready!

Once you've completed these steps, you should be able to:
1. Start the game and see the main menu
2. Enter origin and destination locations
3. Drive the car with realistic physics
4. Navigate using GPS directions
5. Interact with gas stations and repair shops
6. Complete time-based missions

Enjoy your realistic driving simulator experience!
