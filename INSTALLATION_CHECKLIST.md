# 🚗 Realistic Driving Simulator - Installation Checklist

## ✅ **Step-by-Step Installation Guide**

### **Phase 1: Install Unity Hub and Editor**

#### 1. Unity Hub Installation
- [ ] Download Unity Hub from: https://unity3d.com/get-unity/download
- [ ] Run UnityHubSetup.exe
- [ ] Complete installation wizard
- [ ] Launch Unity Hub

#### 2. Unity Editor Installation
- [ ] Open Unity Hub
- [ ] Go to "Installs" tab
- [ ] Click "Install Editor"
- [ ] Select "Unity 2023.3 LTS" (or latest LTS)
- [ ] Add these modules:
  - [ ] Windows Build Support (IL2CPP)
  - [ ] Visual Studio Community 2022 (if needed)
  - [ ] Documentation
- [ ] Wait for installation to complete (may take 30-60 minutes)

### **Phase 2: Blender Installation (Optional but Recommended)**

#### 3. Blender Setup for 3D Assets
- [ ] Download Blender from: https://www.blender.org/download/
- [ ] Install Blender 4.0+ (LTS recommended)
- [ ] Run `SetupBlender.bat` to configure asset pipeline
- [ ] Create vehicle and building models using provided scripts
- [ ] Export models to Unity-compatible FBX format

### **Phase 3: Google Maps API Setup**

#### 4. Google Cloud Console Setup
- [ ] Go to: https://console.cloud.google.com/
- [ ] Sign in with Google account
- [ ] Create new project or select existing
- [ ] Enable these APIs:
  - [ ] Maps JavaScript API
  - [ ] Directions API
  - [ ] Places API (New)
  - [ ] Geocoding API
- [ ] Create API Key:
  - [ ] Go to Credentials → Create Credentials → API Key
  - [ ] Copy and save the API key securely
  - [ ] (Optional) Restrict API key for security

### **Phase 4: Open the Project**

#### 5. Add Project to Unity Hub
- [ ] Open Unity Hub
- [ ] Click "Add" button
- [ ] Navigate to: `C:\Users\<USER>\Desktop\test`
- [ ] Select the folder and click "Add Project"
- [ ] Click on the project to open it

#### 6. Configure API Keys
- [ ] Wait for Unity to import all assets (first time may take 5-10 minutes)
- [ ] In Unity, find "GoogleMapsIntegration" GameObject in the scene
- [ ] In Inspector, paste your API keys:
  - [ ] Google Maps Api Key: [YOUR_API_KEY]
  - [ ] Places Api Key: [YOUR_API_KEY] (can be same)

### **Phase 5: Initial Setup**

#### 7. Run Scene Setup
- [ ] In Unity, find "SceneSetup" script in Assets/Scripts/Utils/
- [ ] Right-click on SceneSetup.cs → Create → [Create GameObject with Script]
- [ ] In Inspector, click "Setup Scene" button
- [ ] This will automatically create all required GameObjects

#### 8. Test Basic Functionality
- [ ] Press Play button in Unity
- [ ] Check Console for any errors
- [ ] Verify main menu appears
- [ ] Test basic car movement (WASD keys)

### **Phase 6: Verification**

#### 9. Verify Installation
- [ ] No red errors in Unity Console
- [ ] Car responds to input (WASD)
- [ ] UI elements are visible
- [ ] Google Maps integration loads (check Console for "Loaded X roads/POIs")

## 🛠️ **Troubleshooting**

### Common Issues:

#### Unity Hub Won't Install
- **Solution**: Run as Administrator
- **Alternative**: Download from Microsoft Store

#### Unity Editor Installation Fails
- **Solution**: Check available disk space (needs ~10GB)
- **Solution**: Disable antivirus temporarily
- **Solution**: Try different Unity version (2022.3 LTS)

#### API Key Not Working
- **Solution**: Verify APIs are enabled in Google Cloud Console
- **Solution**: Check billing is set up (required for Maps API)
- **Solution**: Wait 5-10 minutes for API activation

#### Project Won't Open
- **Solution**: Make sure Unity version matches (2023.3 LTS)
- **Solution**: Delete Library folder and reopen project
- **Solution**: Check Windows permissions on project folder

#### Car Won't Move
- **Solution**: Check Input System is set to "New Input System"
- **Solution**: Verify WheelColliders are properly assigned
- **Solution**: Check car has Rigidbody component

## 📞 **Quick Help Commands**

### If you need to restart setup:
1. **Double-click `OpenProject.bat`** - Opens Unity Hub and guides you
2. **Check `SETUP_GUIDE.md`** - Detailed technical instructions
3. **Review `README.md`** - Project overview and features

### Test Commands in Unity Console:
```csharp
// Test car controller
FindObjectOfType<DrivingSimulator.Vehicle.CarController>()

// Test Google Maps integration
FindObjectOfType<DrivingSimulator.World.GoogleMapsIntegration>()

// Test Game Manager
DrivingSimulator.Managers.GameManager.Instance
```

## 🎯 **Success Indicators**

You'll know everything is working when:
- ✅ Unity opens the project without errors
- ✅ You can see a car in the scene
- ✅ Pressing WASD moves the car
- ✅ Main menu UI is visible
- ✅ Console shows "Loaded X roads from Google Maps"
- ✅ No red error messages in Console

## 🚀 **Next Steps After Installation**

1. **Read the controls**: WASD to drive, E to interact, Space for handbrake
2. **Try a mission**: Enter start/destination in main menu
3. **Explore the world**: Drive around and find gas stations/repair shops
4. **Customize**: Modify scripts to add your own features
5. **Build the game**: File → Build Settings → Build

## 📋 **Installation Summary**

**Total Time Required**: 1-2 hours (depending on download speeds)
**Disk Space Required**: ~15GB (Unity + Project)
**Internet Required**: Yes (for Unity download and Google Maps API)

**What You'll Have After Setup**:
- Fully functional driving simulator
- Real Google Maps integration
- Mission-based gameplay
- Realistic car physics
- Educational location learning
- Expandable codebase for your own features

---

**🎉 Ready to Drive!** Once you complete this checklist, you'll have a fully functional realistic driving simulator running on your Windows 11 system!
