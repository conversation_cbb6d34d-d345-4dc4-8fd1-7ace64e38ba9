using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Complete Dr. Driving Game Manager
/// Handles all game modes, missions, and progression exactly like Dr. Driving
/// </summary>
public class GameManager : MonoBehaviour
{
    [Header("Dr. Driving Game Modes")]
    public enum GameMode
    {
        Highway,
        City,
        Parking,
        Night,
        Rain,
        Desert,
        Tutorial
    }
    
    [Header("Dr. Driving Mission Types")]
    public enum MissionType
    {
        Delivery,
        Passenger,
        Parking,
        TimeTrials,
        TrafficRules,
        FuelEfficiency,
        SpeedLimit,
        NightDriving
    }
    
    [Header("Game State")]
    [SerializeField] private GameMode currentGameMode = GameMode.Highway;
    [SerializeField] private MissionType currentMissionType = MissionType.Delivery;
    [SerializeField] private bool gameStarted = false;
    [SerializeField] private bool missionActive = false;
    [SerializeField] private bool gamePaused = false;
    
    [Header("Dr. Driving UI Panels")]
    [SerializeField] private GameObject mainMenuPanel;
    [SerializeField] private GameObject gameplayPanel;
    [SerializeField] private GameObject pausePanel;
    [SerializeField] private GameObject missionCompletePanel;
    [SerializeField] private GameObject missionFailedPanel;
    [SerializeField] private GameObject garagePanel;
    [SerializeField] private GameObject settingsPanel;
    
    [Header("Dr. Driving HUD Elements")]
    [SerializeField] private Text speedText;
    [SerializeField] private Text fuelText;
    [SerializeField] private Text missionText;
    [SerializeField] private Text timerText;
    [SerializeField] private Text scoreText;
    [SerializeField] private Slider missionProgressSlider;
    [SerializeField] private Image minimap;
    
    [Header("Dr. Driving Mission System")]
    [SerializeField] private Transform[] deliveryPoints;
    [SerializeField] private Transform[] passengerPickupPoints;
    [SerializeField] private Transform[] parkingSpots;
    [SerializeField] private GameObject[] trafficCars;
    
    [Header("Dr. Driving Audio")]
    [SerializeField] private AudioSource musicAudioSource;
    [SerializeField] private AudioSource sfxAudioSource;
    [SerializeField] private AudioClip[] backgroundMusic;
    [SerializeField] private AudioClip missionCompleteSound;
    [SerializeField] private AudioClip missionFailedSound;
    
    // Private variables
    private CarController playerCar;
    private float missionTimer;
    private float missionTimeLimit;
    private int currentScore;
    private int totalScore;
    private int missionsCompleted;
    private int missionsFailed;
    private Vector3 missionTarget;
    private bool missionTargetReached;
    
    // Dr. Driving specific features
    private float trafficViolations;
    private float smoothDrivingScore;
    private float fuelEfficiencyScore;
    private bool perfectParking;
    private List<Mission> availableMissions;
    private Mission currentMission;
    
    [System.Serializable]
    public class Mission
    {
        public string title;
        public string description;
        public MissionType type;
        public Vector3 startPoint;
        public Vector3 endPoint;
        public float timeLimit;
        public int rewardScore;
        public int rewardMoney;
        public bool completed;
    }
    
    void Start()
    {
        InitializeGame();
        SetupMissions();
        ShowMainMenu();
        
        Debug.Log("🎮 Dr. Driving Game Manager initialized");
    }
    
    void Update()
    {
        if (gameStarted && !gamePaused)
        {
            UpdateGameplay();
            UpdateMission();
            UpdateUI();
            CheckInput();
        }
    }
    
    void InitializeGame()
    {
        playerCar = FindObjectOfType<CarController>();
        availableMissions = new List<Mission>();
        
        // Initialize Dr. Driving settings
        Time.timeScale = 1f;
        currentScore = 0;
        totalScore = PlayerPrefs.GetInt("TotalScore", 0);
        missionsCompleted = PlayerPrefs.GetInt("MissionsCompleted", 0);
        
        // Setup audio
        if (musicAudioSource != null && backgroundMusic.Length > 0)
        {
            musicAudioSource.clip = backgroundMusic[Random.Range(0, backgroundMusic.Length)];
            musicAudioSource.loop = true;
            musicAudioSource.Play();
        }
    }
    
    void SetupMissions()
    {
        // Create Dr. Driving style missions
        availableMissions.Add(new Mission
        {
            title = "Pizza Delivery",
            description = "Deliver pizza to customer in 3 minutes",
            type = MissionType.Delivery,
            startPoint = Vector3.zero,
            endPoint = new Vector3(100, 0, 200),
            timeLimit = 180f,
            rewardScore = 500,
            rewardMoney = 50
        });
        
        availableMissions.Add(new Mission
        {
            title = "Airport Pickup",
            description = "Pick up passenger from airport",
            type = MissionType.Passenger,
            startPoint = Vector3.zero,
            endPoint = new Vector3(-150, 0, 300),
            timeLimit = 240f,
            rewardScore = 750,
            rewardMoney = 75
        });
        
        availableMissions.Add(new Mission
        {
            title = "Perfect Parking",
            description = "Park in the designated spot without hitting anything",
            type = MissionType.Parking,
            startPoint = Vector3.zero,
            endPoint = new Vector3(50, 0, 100),
            timeLimit = 120f,
            rewardScore = 1000,
            rewardMoney = 100
        });
        
        availableMissions.Add(new Mission
        {
            title = "Night Delivery",
            description = "Deliver package during night time",
            type = MissionType.NightDriving,
            startPoint = Vector3.zero,
            endPoint = new Vector3(200, 0, -100),
            timeLimit = 300f,
            rewardScore = 800,
            rewardMoney = 80
        });
        
        availableMissions.Add(new Mission
        {
            title = "Fuel Economy Challenge",
            description = "Complete route using minimal fuel",
            type = MissionType.FuelEfficiency,
            startPoint = Vector3.zero,
            endPoint = new Vector3(300, 0, 150),
            timeLimit = 360f,
            rewardScore = 600,
            rewardMoney = 60
        });
    }
    
    public void StartGame(GameMode mode)
    {
        currentGameMode = mode;
        gameStarted = true;
        missionActive = false;
        
        ShowGameplayUI();
        
        // Load appropriate scene based on mode
        switch (mode)
        {
            case GameMode.Highway:
                // Highway driving setup
                SetupHighwayMode();
                break;
            case GameMode.City:
                // City driving setup
                SetupCityMode();
                break;
            case GameMode.Parking:
                // Parking challenge setup
                SetupParkingMode();
                break;
            case GameMode.Night:
                // Night driving setup
                SetupNightMode();
                break;
            case GameMode.Rain:
                // Rain driving setup
                SetupRainMode();
                break;
        }
        
        Debug.Log($"🚗 Started Dr. Driving - {mode} mode");
    }
    
    public void StartMission(int missionIndex)
    {
        if (missionIndex < availableMissions.Count)
        {
            currentMission = availableMissions[missionIndex];
            currentMissionType = currentMission.type;
            missionActive = true;
            missionTimer = 0f;
            missionTimeLimit = currentMission.timeLimit;
            missionTarget = currentMission.endPoint;
            missionTargetReached = false;
            
            // Position player at mission start
            if (playerCar != null)
            {
                playerCar.transform.position = currentMission.startPoint;
                playerCar.transform.rotation = Quaternion.identity;
            }
            
            // Update mission UI
            if (missionText != null)
            {
                missionText.text = currentMission.title + "\n" + currentMission.description;
            }
            
            Debug.Log($"🎯 Mission started: {currentMission.title}");
        }
    }
    
    void UpdateGameplay()
    {
        if (playerCar == null) return;
        
        // Update smooth driving score
        float currentSpeed = playerCar.GetCurrentSpeed();
        if (currentSpeed > 0)
        {
            // Reward smooth acceleration/deceleration
            smoothDrivingScore += Time.deltaTime * (1f - Mathf.Abs(Input.GetAxis("Vertical")));
        }
        
        // Check for traffic violations
        CheckTrafficViolations();
        
        // Update fuel efficiency score
        if (currentSpeed > 0)
        {
            fuelEfficiencyScore += Time.deltaTime * (currentSpeed / 100f);
        }
    }
    
    void UpdateMission()
    {
        if (!missionActive || currentMission == null) return;
        
        missionTimer += Time.deltaTime;
        
        // Check mission completion
        switch (currentMission.type)
        {
            case MissionType.Delivery:
            case MissionType.Passenger:
                CheckDeliveryMission();
                break;
            case MissionType.Parking:
                CheckParkingMission();
                break;
            case MissionType.FuelEfficiency:
                CheckFuelEfficiencyMission();
                break;
        }
        
        // Check time limit
        if (missionTimer >= missionTimeLimit)
        {
            FailMission("Time's up!");
        }
    }
    
    void CheckDeliveryMission()
    {
        if (playerCar == null) return;
        
        float distanceToTarget = Vector3.Distance(playerCar.transform.position, missionTarget);
        
        if (distanceToTarget < 10f && !missionTargetReached)
        {
            CompleteMission();
        }
        
        // Update progress
        if (missionProgressSlider != null)
        {
            float totalDistance = Vector3.Distance(currentMission.startPoint, currentMission.endPoint);
            float currentDistance = Vector3.Distance(playerCar.transform.position, currentMission.startPoint);
            missionProgressSlider.value = currentDistance / totalDistance;
        }
    }
    
    void CheckParkingMission()
    {
        if (playerCar == null) return;
        
        float distanceToTarget = Vector3.Distance(playerCar.transform.position, missionTarget);
        float speed = playerCar.GetCurrentSpeed();
        
        // Perfect parking requires being in the spot with very low speed
        if (distanceToTarget < 5f && speed < 2f)
        {
            perfectParking = true;
            CompleteMission();
        }
    }
    
    void CheckFuelEfficiencyMission()
    {
        if (playerCar == null) return;
        
        float distanceToTarget = Vector3.Distance(playerCar.transform.position, missionTarget);
        
        if (distanceToTarget < 10f)
        {
            // Bonus score for fuel efficiency
            float fuelBonus = playerCar.GetCurrentFuel() * 10f;
            currentScore += Mathf.RoundToInt(fuelBonus);
            CompleteMission();
        }
    }
    
    void CheckTrafficViolations()
    {
        if (playerCar == null) return;
        
        float speed = playerCar.GetCurrentSpeed();
        
        // Speed limit violations (Dr. Driving feature)
        if (speed > 80f) // Assuming 80 km/h speed limit
        {
            trafficViolations += Time.deltaTime;
        }
        
        // Add other traffic rule checks here
    }
    
    void CompleteMission()
    {
        if (!missionActive) return;
        
        missionActive = false;
        missionTargetReached = true;
        
        // Calculate final score
        int timeBonus = Mathf.RoundToInt((missionTimeLimit - missionTimer) * 10f);
        int smoothBonus = Mathf.RoundToInt(smoothDrivingScore * 5f);
        int violationPenalty = Mathf.RoundToInt(trafficViolations * -50f);
        
        int finalScore = currentMission.rewardScore + timeBonus + smoothBonus + violationPenalty;
        currentScore += finalScore;
        totalScore += finalScore;
        missionsCompleted++;
        
        // Save progress
        PlayerPrefs.SetInt("TotalScore", totalScore);
        PlayerPrefs.SetInt("MissionsCompleted", missionsCompleted);
        
        // Play success sound
        if (sfxAudioSource != null && missionCompleteSound != null)
        {
            sfxAudioSource.PlayOneShot(missionCompleteSound);
        }
        
        // Show completion UI
        ShowMissionComplete(finalScore);
        
        Debug.Log($"🎉 Mission completed! Score: {finalScore}");
    }
    
    void FailMission(string reason)
    {
        if (!missionActive) return;
        
        missionActive = false;
        missionsFailed++;
        
        // Play failure sound
        if (sfxAudioSource != null && missionFailedSound != null)
        {
            sfxAudioSource.PlayOneShot(missionFailedSound);
        }
        
        // Show failure UI
        ShowMissionFailed(reason);
        
        Debug.Log($"❌ Mission failed: {reason}");
    }
    
    void UpdateUI()
    {
        // Update HUD elements
        if (speedText != null && playerCar != null)
        {
            speedText.text = Mathf.RoundToInt(playerCar.GetCurrentSpeed()) + " km/h";
        }
        
        if (fuelText != null && playerCar != null)
        {
            fuelText.text = "Fuel: " + Mathf.RoundToInt(playerCar.GetCurrentFuel()) + "%";
        }
        
        if (timerText != null && missionActive)
        {
            float remainingTime = missionTimeLimit - missionTimer;
            int minutes = Mathf.FloorToInt(remainingTime / 60f);
            int seconds = Mathf.FloorToInt(remainingTime % 60f);
            timerText.text = string.Format("{0:00}:{1:00}", minutes, seconds);
        }
        
        if (scoreText != null)
        {
            scoreText.text = "Score: " + currentScore;
        }
    }
    
    void CheckInput()
    {
        // Pause game
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            TogglePause();
        }
        
        // Quick restart
        if (Input.GetKeyDown(KeyCode.R))
        {
            RestartMission();
        }
    }
    
    // UI Methods
    public void ShowMainMenu()
    {
        SetActivePanel(mainMenuPanel);
        gameStarted = false;
        Time.timeScale = 1f;
    }
    
    public void ShowGameplayUI()
    {
        SetActivePanel(gameplayPanel);
    }
    
    public void ShowMissionComplete(int score)
    {
        SetActivePanel(missionCompletePanel);
        // Update completion UI with score details
    }
    
    public void ShowMissionFailed(string reason)
    {
        SetActivePanel(missionFailedPanel);
        // Update failure UI with reason
    }
    
    public void TogglePause()
    {
        gamePaused = !gamePaused;
        Time.timeScale = gamePaused ? 0f : 1f;
        
        if (gamePaused)
            SetActivePanel(pausePanel);
        else
            SetActivePanel(gameplayPanel);
    }
    
    public void RestartMission()
    {
        if (currentMission != null)
        {
            StartMission(availableMissions.IndexOf(currentMission));
        }
    }
    
    public void NextMission()
    {
        int nextIndex = availableMissions.IndexOf(currentMission) + 1;
        if (nextIndex < availableMissions.Count)
        {
            StartMission(nextIndex);
        }
        else
        {
            ShowMainMenu();
        }
    }
    
    void SetActivePanel(GameObject panel)
    {
        // Hide all panels
        if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
        if (gameplayPanel != null) gameplayPanel.SetActive(false);
        if (pausePanel != null) pausePanel.SetActive(false);
        if (missionCompletePanel != null) missionCompletePanel.SetActive(false);
        if (missionFailedPanel != null) missionFailedPanel.SetActive(false);
        if (garagePanel != null) garagePanel.SetActive(false);
        if (settingsPanel != null) settingsPanel.SetActive(false);
        
        // Show target panel
        if (panel != null) panel.SetActive(true);
    }
    
    // Mode setup methods
    void SetupHighwayMode()
    {
        // Setup highway environment
        Debug.Log("🛣️ Highway mode setup");
    }
    
    void SetupCityMode()
    {
        // Setup city environment
        Debug.Log("🏙️ City mode setup");
    }
    
    void SetupParkingMode()
    {
        // Setup parking challenges
        Debug.Log("🅿️ Parking mode setup");
    }
    
    void SetupNightMode()
    {
        // Setup night lighting
        RenderSettings.ambientLight = Color.black;
        Debug.Log("🌙 Night mode setup");
    }
    
    void SetupRainMode()
    {
        // Setup rain effects
        Debug.Log("🌧️ Rain mode setup");
    }
    
    // Public getters
    public bool IsGameStarted() => gameStarted;
    public bool IsMissionActive() => missionActive;
    public int GetCurrentScore() => currentScore;
    public int GetTotalScore() => totalScore;
    public int GetMissionsCompleted() => missionsCompleted;
}
