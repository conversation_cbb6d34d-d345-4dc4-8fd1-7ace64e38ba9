# MapLoader.gd - FREE Map Integration using OpenStreetMap
# No API costs, completely free alternative to Google Maps

extends Node
class_name MapLoader

# OpenStreetMap API endpoints (FREE)
const OSM_API_BASE = "https://api.openstreetmap.org/api/0.6/"
const OVERPASS_API = "https://overpass-api.de/api/interpreter"
const NOMINATIM_API = "https://nominatim.openstreetmap.org/"

# Map data
var current_bounds: Dictionary = {}
var loaded_roads: Array = []
var loaded_buildings: Array = []
var loaded_pois: Array = []

# HTTP requests
var http_request: HTTPRequest

# Signals
signal map_data_loaded(roads: Array, buildings: Array, pois: Array)
signal location_found(location_name: String, coordinates: Vector2)

func _ready():
	print("🗺️ FREE Map Loader initialized (OpenStreetMap)")
	setup_http_request()

func setup_http_request():
	"""Set up HTTP request for API calls"""
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_request_completed)

func load_map_area(center_lat: float, center_lon: float, radius_km: float = 2.0):
	"""Load map data for a specific area using FREE OpenStreetMap API"""
	print("📍 Loading map data for: %.6f, %.6f (radius: %.1f km)" % [center_lat, center_lon, radius_km])
	
	# Calculate bounding box
	var lat_offset = radius_km / 111.0  # Approximate km to degrees
	var lon_offset = radius_km / (111.0 * cos(deg_to_rad(center_lat)))
	
	current_bounds = {
		"south": center_lat - lat_offset,
		"west": center_lon - lon_offset,
		"north": center_lat + lat_offset,
		"east": center_lon + lon_offset
	}
	
	# Load roads first
	load_roads()

func load_roads():
	"""Load road data from OpenStreetMap (FREE)"""
	var overpass_query = """
	[out:json][timeout:25];
	(
	  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|residential|service)$"](%f,%f,%f,%f);
	);
	out geom;
	""" % [current_bounds.south, current_bounds.west, current_bounds.north, current_bounds.east]
	
	var headers = ["Content-Type: application/x-www-form-urlencoded"]
	var body = "data=" + overpass_query.uri_encode()
	
	print("🛣️ Requesting road data from OpenStreetMap...")
	http_request.request(OVERPASS_API, headers, HTTPClient.METHOD_POST, body)

func load_buildings():
	"""Load building data from OpenStreetMap (FREE)"""
	var overpass_query = """
	[out:json][timeout:25];
	(
	  way["building"](%f,%f,%f,%f);
	  relation["building"](%f,%f,%f,%f);
	);
	out geom;
	""" % [current_bounds.south, current_bounds.west, current_bounds.north, current_bounds.east,
	       current_bounds.south, current_bounds.west, current_bounds.north, current_bounds.east]
	
	var headers = ["Content-Type: application/x-www-form-urlencoded"]
	var body = "data=" + overpass_query.uri_encode()
	
	print("🏢 Requesting building data from OpenStreetMap...")
	http_request.request(OVERPASS_API, headers, HTTPClient.METHOD_POST, body)

func load_pois():
	"""Load Points of Interest from OpenStreetMap (FREE)"""
	var overpass_query = """
	[out:json][timeout:25];
	(
	  node["amenity"~"^(fuel|car_repair|restaurant|hospital|bank)$"](%f,%f,%f,%f);
	  way["amenity"~"^(fuel|car_repair|restaurant|hospital|bank)$"](%f,%f,%f,%f);
	);
	out geom;
	""" % [current_bounds.south, current_bounds.west, current_bounds.north, current_bounds.east,
	       current_bounds.south, current_bounds.west, current_bounds.north, current_bounds.east]
	
	var headers = ["Content-Type: application/x-www-form-urlencoded"]
	var body = "data=" + overpass_query.uri_encode()
	
	print("📍 Requesting POI data from OpenStreetMap...")
	http_request.request(OVERPASS_API, headers, HTTPClient.METHOD_POST, body)

func search_location(location_name: String):
	"""Search for a location using FREE Nominatim API"""
	var search_url = NOMINATIM_API + "search?q=" + location_name.uri_encode() + "&format=json&limit=1"
	
	print("🔍 Searching for location: %s" % location_name)
	http_request.request(search_url)

func _on_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""Handle HTTP request completion"""
	if response_code != 200:
		print("❌ Request failed with code: %d" % response_code)
		return
	
	var json_string = body.get_string_from_utf8()
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("❌ Failed to parse JSON response")
		return
	
	var data = json.data
	
	# Determine what type of data this is based on the response
	if data.has("elements"):
		# This is Overpass API data (roads, buildings, POIs)
		process_overpass_data(data)
	elif data is Array and data.size() > 0 and data[0].has("lat"):
		# This is Nominatim search result
		process_nominatim_data(data)

func process_overpass_data(data: Dictionary):
	"""Process data from Overpass API"""
	var elements = data.get("elements", [])
	
	for element in elements:
		var tags = element.get("tags", {})
		
		if tags.has("highway"):
			# This is a road
			process_road_element(element)
		elif tags.has("building"):
			# This is a building
			process_building_element(element)
		elif tags.has("amenity"):
			# This is a POI
			process_poi_element(element)
	
	print("✅ Processed %d map elements" % elements.size())
	
	# After processing all data, emit signal
	map_data_loaded.emit(loaded_roads, loaded_buildings, loaded_pois)

func process_road_element(element: Dictionary):
	"""Process a road element from OpenStreetMap"""
	var road_data = {
		"id": element.get("id", 0),
		"type": element.get("tags", {}).get("highway", "unknown"),
		"name": element.get("tags", {}).get("name", "Unnamed Road"),
		"geometry": []
	}
	
	# Extract geometry
	var geometry = element.get("geometry", [])
	for point in geometry:
		var world_pos = latlon_to_world(point.lat, point.lon)
		road_data.geometry.append(world_pos)
	
	loaded_roads.append(road_data)

func process_building_element(element: Dictionary):
	"""Process a building element from OpenStreetMap"""
	var building_data = {
		"id": element.get("id", 0),
		"type": element.get("tags", {}).get("building", "yes"),
		"name": element.get("tags", {}).get("name", "Building"),
		"height": element.get("tags", {}).get("height", "10").to_float(),
		"geometry": []
	}
	
	# Extract geometry
	var geometry = element.get("geometry", [])
	for point in geometry:
		var world_pos = latlon_to_world(point.lat, point.lon)
		building_data.geometry.append(world_pos)
	
	loaded_buildings.append(building_data)

func process_poi_element(element: Dictionary):
	"""Process a POI element from OpenStreetMap"""
	var poi_data = {
		"id": element.get("id", 0),
		"type": element.get("tags", {}).get("amenity", "unknown"),
		"name": element.get("tags", {}).get("name", "Point of Interest"),
		"position": Vector3.ZERO
	}
	
	# Get position
	if element.has("lat") and element.has("lon"):
		poi_data.position = latlon_to_world(element.lat, element.lon)
	elif element.has("geometry") and element.geometry.size() > 0:
		var center_point = element.geometry[0]
		poi_data.position = latlon_to_world(center_point.lat, center_point.lon)
	
	loaded_pois.append(poi_data)

func process_nominatim_data(data: Array):
	"""Process location search results from Nominatim"""
	if data.size() == 0:
		print("❌ No location found")
		return
	
	var location = data[0]
	var lat = location.get("lat", "0").to_float()
	var lon = location.get("lon", "0").to_float()
	var display_name = location.get("display_name", "Unknown Location")
	
	print("📍 Found location: %s at %.6f, %.6f" % [display_name, lat, lon])
	location_found.emit(display_name, Vector2(lat, lon))

func latlon_to_world(lat: float, lon: float) -> Vector3:
	"""Convert latitude/longitude to world coordinates"""
	# Simple conversion - in a real implementation, you'd use proper map projection
	# This assumes a local coordinate system centered on the map area
	
	var center_lat = (current_bounds.north + current_bounds.south) / 2.0
	var center_lon = (current_bounds.east + current_bounds.west) / 2.0
	
	# Convert to meters (approximate)
	var x = (lon - center_lon) * 111320.0 * cos(deg_to_rad(center_lat))
	var z = (lat - center_lat) * 111320.0
	
	return Vector3(x, 0, z)

func world_to_latlon(world_pos: Vector3) -> Vector2:
	"""Convert world coordinates back to latitude/longitude"""
	var center_lat = (current_bounds.north + current_bounds.south) / 2.0
	var center_lon = (current_bounds.east + current_bounds.west) / 2.0
	
	var lat = center_lat + (world_pos.z / 111320.0)
	var lon = center_lon + (world_pos.x / (111320.0 * cos(deg_to_rad(center_lat))))
	
	return Vector2(lat, lon)

func create_road_mesh(road_data: Dictionary) -> MeshInstance3D:
	"""Create a 3D mesh for a road"""
	var mesh_instance = MeshInstance3D.new()
	var array_mesh = ArrayMesh.new()
	var arrays = []
	arrays.resize(Mesh.ARRAY_MAX)
	
	var vertices = PackedVector3Array()
	var indices = PackedInt32Array()
	var uvs = PackedVector2Array()
	
	var geometry = road_data.geometry
	if geometry.size() < 2:
		return mesh_instance
	
	# Create road strip
	var road_width = get_road_width(road_data.type)
	
	for i in range(geometry.size() - 1):
		var p1 = geometry[i]
		var p2 = geometry[i + 1]
		
		var direction = (p2 - p1).normalized()
		var perpendicular = Vector3(-direction.z, 0, direction.x) * road_width * 0.5
		
		# Add vertices for road segment
		var base_index = vertices.size()
		vertices.append(p1 - perpendicular)
		vertices.append(p1 + perpendicular)
		vertices.append(p2 + perpendicular)
		vertices.append(p2 - perpendicular)
		
		# Add UVs
		uvs.append(Vector2(0, i))
		uvs.append(Vector2(1, i))
		uvs.append(Vector2(1, i + 1))
		uvs.append(Vector2(0, i + 1))
		
		# Add triangles
		indices.append(base_index)
		indices.append(base_index + 1)
		indices.append(base_index + 2)
		
		indices.append(base_index)
		indices.append(base_index + 2)
		indices.append(base_index + 3)
	
	arrays[Mesh.ARRAY_VERTEX] = vertices
	arrays[Mesh.ARRAY_INDEX] = indices
	arrays[Mesh.ARRAY_TEX_UV] = uvs
	
	array_mesh.add_surface_from_arrays(Mesh.PRIMITIVE_TRIANGLES, arrays)
	mesh_instance.mesh = array_mesh
	
	# Add road material
	var material = StandardMaterial3D.new()
	material.albedo_color = Color(0.3, 0.3, 0.3)  # Dark gray for asphalt
	material.roughness = 0.8
	mesh_instance.set_surface_override_material(0, material)
	
	return mesh_instance

func get_road_width(road_type: String) -> float:
	"""Get road width based on type"""
	match road_type:
		"motorway":
			return 12.0
		"trunk":
			return 10.0
		"primary":
			return 8.0
		"secondary":
			return 6.0
		"tertiary":
			return 5.0
		"residential":
			return 4.0
		"service":
			return 3.0
		_:
			return 4.0

func create_building_mesh(building_data: Dictionary) -> MeshInstance3D:
	"""Create a 3D mesh for a building"""
	var mesh_instance = MeshInstance3D.new()
	
	# Simple box building for now
	var box_mesh = BoxMesh.new()
	box_mesh.size = Vector3(10, building_data.height, 10)
	mesh_instance.mesh = box_mesh
	
	# Position at center of building footprint
	if building_data.geometry.size() > 0:
		var center = Vector3.ZERO
		for point in building_data.geometry:
			center += point
		center /= building_data.geometry.size()
		mesh_instance.position = center
		mesh_instance.position.y = building_data.height * 0.5
	
	# Add building material
	var material = StandardMaterial3D.new()
	material.albedo_color = Color(0.8, 0.7, 0.6)  # Beige building color
	mesh_instance.set_surface_override_material(0, material)
	
	return mesh_instance

# Public API methods
func get_loaded_roads() -> Array:
	return loaded_roads

func get_loaded_buildings() -> Array:
	return loaded_buildings

func get_loaded_pois() -> Array:
	return loaded_pois

func clear_map_data():
	"""Clear all loaded map data"""
	loaded_roads.clear()
	loaded_buildings.clear()
	loaded_pois.clear()
	current_bounds.clear()
	print("🗑️ Map data cleared")
