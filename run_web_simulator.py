#!/usr/bin/env python3
"""
🌐 FREE Web-Based Driving Simulator Server
Runs the driving simulator in your web browser - no downloads needed!
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# Configuration
PORT = 8000
HOST = 'localhost'

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve the driving simulator"""
    
    def end_headers(self):
        # Add headers for better compatibility
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Custom logging
        print(f"🌐 {self.address_string()} - {format % args}")

def print_banner():
    """Print startup banner"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🌐 FREE WEB DRIVING SIMULATOR SERVER 🌐               ║
║                                                              ║
║              No Downloads • No Installation                  ║
║                    100% Browser-Based                        ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

🎮 Starting FREE Realistic Driving Simulator...
💰 Total Cost: $0.00 (Everything is FREE!)
🌐 Platform: Any web browser
📱 Mobile: Touch controls included
🚀 Performance: Optimized for all devices
""")

def check_files():
    """Check if required files exist"""
    required_files = ['index.html']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        print("Please make sure index.html is in the current directory.")
        return False
    
    print("✅ All required files found")
    return True

def start_server():
    """Start the web server"""
    try:
        # Change to the script directory
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        print_banner()
        
        if not check_files():
            return
        
        # Create server
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            server_url = f"http://{HOST}:{PORT}"
            
            print(f"🚀 Server starting on {server_url}")
            print(f"📁 Serving files from: {os.getcwd()}")
            print(f"🌐 Opening browser automatically...")
            print(f"")
            print(f"🎮 GAME CONTROLS:")
            print(f"   W/A/S/D or Arrow Keys - Drive the car")
            print(f"   Space - Handbrake")
            print(f"   📱 Mobile: Swipe to control")
            print(f"")
            print(f"🎯 GAME FEATURES:")
            print(f"   ✅ Realistic car physics")
            print(f"   ✅ Mission-based gameplay")
            print(f"   ✅ Fuel and damage systems")
            print(f"   ✅ GPS navigation")
            print(f"   ✅ Money management")
            print(f"   ✅ Educational geography")
            print(f"")
            print(f"🔧 SERVER INFO:")
            print(f"   URL: {server_url}")
            print(f"   Port: {PORT}")
            print(f"   Status: Running")
            print(f"")
            print(f"⚠️  Press Ctrl+C to stop the server")
            print(f"═" * 66)
            
            # Open browser automatically
            try:
                webbrowser.open(server_url)
                print(f"✅ Browser opened automatically")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print(f"📌 Please manually open: {server_url}")
            
            print(f"")
            print(f"🎉 FREE Driving Simulator is now running!")
            print(f"🌐 Access it at: {server_url}")
            print(f"")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n")
        print(f"🛑 Server stopped by user")
        print(f"👋 Thanks for using the FREE Driving Simulator!")
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use!")
            print(f"💡 Try a different port or close other applications using port {PORT}")
            print(f"🔧 You can also try: python run_web_simulator.py --port 8001")
        else:
            print(f"❌ Server error: {e}")
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"🔧 Please check your Python installation and try again")

def main():
    """Main function with command line argument support"""
    global PORT
    
    # Simple command line argument parsing
    if len(sys.argv) > 1:
        for i, arg in enumerate(sys.argv):
            if arg == '--port' and i + 1 < len(sys.argv):
                try:
                    PORT = int(sys.argv[i + 1])
                    print(f"🔧 Using custom port: {PORT}")
                except ValueError:
                    print(f"❌ Invalid port number: {sys.argv[i + 1]}")
                    return
            elif arg == '--help' or arg == '-h':
                print(f"""
🌐 FREE Web Driving Simulator Server

Usage: python run_web_simulator.py [options]

Options:
  --port PORT    Use custom port (default: 8000)
  --help, -h     Show this help message

Examples:
  python run_web_simulator.py
  python run_web_simulator.py --port 8001

Features:
  🚗 Realistic driving physics
  🗺️ GPS navigation system  
  🎯 Mission-based gameplay
  ⛽ Fuel management
  💰 Money system
  📱 Mobile-friendly controls
  🆓 100% FREE - no ads, no payments

The simulator will automatically open in your default web browser.
Works on Windows, Mac, Linux, and mobile devices.
""")
                return
    
    start_server()

if __name__ == "__main__":
    main()
