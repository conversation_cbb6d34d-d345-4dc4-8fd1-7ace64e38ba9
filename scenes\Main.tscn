[gd_scene load_steps=8 format=3 uid="uid://bqvh8j8j8j8j8"]

[ext_resource type="Script" path="res://scripts/GameManager.gd" id="1_1"]
[ext_resource type="Script" path="res://scripts/CarController.gd" id="2_1"]
[ext_resource type="Script" path="res://scripts/MapLoader.gd" id="3_1"]
[ext_resource type="Script" path="res://scripts/UIManager.gd" id="4_1"]

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.5, 0.7, 1, 1)
ambient_light_source = 2
ambient_light_color = Color(0.8, 0.8, 0.9, 1)
ambient_light_energy = 0.3

[sub_resource type="PlaneMesh" id="PlaneMesh_1"]
size = Vector2(2000, 2000)

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(2, 1, 4)

[node name="Main" type="Node3D"]

[node name="GameManager" type="Node" parent="."]
script = ExtResource("1_1")

[node name="MapLoader" type="Node" parent="."]
script = ExtResource("3_1")

[node name="World" type="Node3D" parent="."]

[node name="Ground" type="MeshInstance3D" parent="World"]
mesh = SubResource("PlaneMesh_1")

[node name="PlayerCar" type="RigidBody3D" parent="World" groups=["player_car"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
mass = 1500.0
script = ExtResource("2_1")

[node name="CarBody" type="MeshInstance3D" parent="World/PlayerCar"]
mesh = SubResource("BoxMesh_1")

[node name="CollisionShape3D" type="CollisionShape3D" parent="World/PlayerCar"]

[node name="EngineAudio" type="AudioStreamPlayer3D" parent="World/PlayerCar"]

[node name="BrakeAudio" type="AudioStreamPlayer3D" parent="World/PlayerCar"]

[node name="Wheels" type="Node3D" parent="World/PlayerCar"]

[node name="FrontLeft" type="MeshInstance3D" parent="World/PlayerCar/Wheels"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, -1.5, -0.3, 1.5)

[node name="FrontRight" type="MeshInstance3D" parent="World/PlayerCar/Wheels"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, 1.5, -0.3, 1.5)

[node name="RearLeft" type="MeshInstance3D" parent="World/PlayerCar/Wheels"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, -1.5, -0.3, -1.5)

[node name="RearRight" type="MeshInstance3D" parent="World/PlayerCar/Wheels"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, 1.5, -0.3, -1.5)

[node name="Camera3D" type="Camera3D" parent="World/PlayerCar"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 2, 3)

[node name="Environment" type="Node3D" parent="World"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.0

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")

[node name="UI" type="Control" parent="." groups=["ui_manager"]]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("4_1")

[node name="MainMenuPanel" type="Control" parent="UI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/MainMenuPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = 100.0

[node name="TitleLabel" type="Label" parent="UI/MainMenuPanel/VBoxContainer"]
layout_mode = 2
text = "FREE Driving Simulator"
horizontal_alignment = 1

[node name="NewMissionButton" type="Button" parent="UI/MainMenuPanel/VBoxContainer"]
layout_mode = 2
text = "New Mission"

[node name="ContinueButton" type="Button" parent="UI/MainMenuPanel/VBoxContainer"]
layout_mode = 2
text = "Continue"

[node name="SettingsButton" type="Button" parent="UI/MainMenuPanel/VBoxContainer"]
layout_mode = 2
text = "Settings"

[node name="QuitButton" type="Button" parent="UI/MainMenuPanel/VBoxContainer"]
layout_mode = 2
text = "Quit"

[node name="MissionBriefingPanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/MissionBriefingPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0

[node name="MissionDescription" type="Label" parent="UI/MissionBriefingPanel/VBoxContainer"]
layout_mode = 2
text = "Mission Description"
autowrap_mode = 2

[node name="StartMissionButton" type="Button" parent="UI/MissionBriefingPanel/VBoxContainer"]
layout_mode = 2
text = "Start Mission"

[node name="CancelMissionButton" type="Button" parent="UI/MissionBriefingPanel/VBoxContainer"]
layout_mode = 2
text = "Cancel"

[node name="DrivingHUDPanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="SpeedLabel" type="Label" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.02
anchor_top = 0.02
anchor_right = 0.3
anchor_bottom = 0.1
text = "Speed: 0 km/h"

[node name="FuelBar" type="ProgressBar" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.02
anchor_top = 0.12
anchor_right = 0.3
anchor_bottom = 0.16
max_value = 100.0
value = 100.0

[node name="DamageBar" type="ProgressBar" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.02
anchor_top = 0.18
anchor_right = 0.3
anchor_bottom = 0.22
max_value = 100.0

[node name="TimerLabel" type="Label" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.7
anchor_top = 0.02
anchor_right = 0.98
anchor_bottom = 0.1
text = "Time: 10:00"
horizontal_alignment = 2

[node name="MoneyLabel" type="Label" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.7
anchor_top = 0.12
anchor_right = 0.98
anchor_bottom = 0.2
text = "Money: $1000"
horizontal_alignment = 2

[node name="GPSLabel" type="Label" parent="UI/DrivingHUDPanel"]
layout_mode = 0
anchor_left = 0.02
anchor_top = 0.8
anchor_right = 0.5
anchor_bottom = 0.98
text = "GPS: Follow the road ahead"
autowrap_mode = 2

[node name="MissionCompletePanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MissionFailedPanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="PauseMenuPanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="ShopPanel" type="Control" parent="UI"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
