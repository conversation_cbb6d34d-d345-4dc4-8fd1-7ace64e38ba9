"""
Advanced Vehicle Creator for Blender
Creates detailed, game-ready vehicle models for Unity integration
"""

import bpy
import bmesh
from mathutils import Vector, Matrix
import math

class DetailedVehicleCreator:
    """Creates detailed vehicle models with proper topology for games"""
    
    def __init__(self):
        self.scale_factor = 1.0  # Unity scale
        
    def create_sedan(self, name="RealisticSedan"):
        """Create a detailed sedan model"""
        print(f"🚗 Creating detailed sedan: {name}")
        
        # Clear selection
        bpy.ops.object.select_all(action='DESELECT')
        
        # Create main body
        body = self._create_car_body(name)
        
        # Create wheels
        wheels = self._create_wheels(name, body)
        
        # Create interior
        interior = self._create_interior(name, body)
        
        # Create details
        details = self._create_car_details(name, body)
        
        # Apply materials
        self._apply_car_materials(body, wheels, interior, details)
        
        print(f"✅ Sedan '{name}' created successfully")
        return body
    
    def _create_car_body(self, name):
        """Create the main car body with proper topology"""
        # Start with a cube and modify it
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0.8))
        body = bpy.context.active_object
        body.name = f"{name}_Body"
        
        # Enter edit mode for detailed modeling
        bpy.context.view_layer.objects.active = body
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Get bmesh representation
        bm = bmesh.from_mesh(body.data)
        
        # Scale to car proportions (length, width, height)
        bmesh.ops.scale(bm, vec=(2.2, 4.5, 0.7), verts=bm.verts)
        
        # Add more geometry for detail
        bmesh.ops.subdivide_edges(bm, 
                                 edges=bm.edges[:], 
                                 cuts=2, 
                                 use_grid_fill=True)
        
        # Create car shape by moving vertices
        self._shape_car_body(bm)
        
        # Update mesh
        bm.to_mesh(body.data)
        bm.free()
        
        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')
        
        # Add subdivision surface modifier for smoothness
        subdiv = body.modifiers.new(name="Subdivision", type='SUBSURF')
        subdiv.levels = 1
        subdiv.render_levels = 2
        
        return body
    
    def _shape_car_body(self, bm):
        """Shape the bmesh into a car-like form"""
        # This is a simplified version - in practice, you'd do detailed vertex manipulation
        for vert in bm.verts:
            # Lower the bottom
            if vert.co.z < -0.3:
                vert.co.z *= 0.3
            
            # Create windshield angle
            if vert.co.y > 1.5 and vert.co.z > 0.2:
                vert.co.z *= 1.3
                vert.co.y *= 0.9
            
            # Create rear slope
            if vert.co.y < -1.5 and vert.co.z > 0.2:
                vert.co.z *= 1.1
                vert.co.y *= 0.95
    
    def _create_wheels(self, name, parent):
        """Create detailed wheels with rims and tires"""
        wheel_positions = [
            (-1.4, 1.8, 0.35, "FrontLeft"),
            (1.4, 1.8, 0.35, "FrontRight"),
            (-1.4, -1.8, 0.35, "RearLeft"),
            (1.4, -1.8, 0.35, "RearRight")
        ]
        
        wheels = []
        
        for x, y, z, wheel_name in wheel_positions:
            # Create tire
            bpy.ops.mesh.primitive_torus_add(
                major_radius=0.4, 
                minor_radius=0.15, 
                location=(x, y, z)
            )
            tire = bpy.context.active_object
            tire.name = f"{name}_Tire_{wheel_name}"
            tire.rotation_euler = (math.pi/2, 0, 0)
            
            # Create rim
            bpy.ops.mesh.primitive_cylinder_add(
                radius=0.3, 
                depth=0.2, 
                location=(x, y, z)
            )
            rim = bpy.context.active_object
            rim.name = f"{name}_Rim_{wheel_name}"
            rim.rotation_euler = (math.pi/2, 0, 0)
            
            # Add rim details
            self._add_rim_details(rim)
            
            # Parent to main body
            tire.parent = parent
            rim.parent = parent
            
            wheels.extend([tire, rim])
        
        return wheels
    
    def _add_rim_details(self, rim):
        """Add spoke details to the rim"""
        bpy.context.view_layer.objects.active = rim
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Add inset for spoke pattern
        bpy.ops.mesh.select_all(action='SELECT')
        bpy.ops.mesh.inset_faces(thickness=0.05, depth=0.02)
        
        bpy.ops.object.mode_set(mode='OBJECT')
    
    def _create_interior(self, name, parent):
        """Create basic interior elements"""
        interior_objects = []
        
        # Dashboard
        bpy.ops.mesh.primitive_cube_add(
            size=1, 
            location=(0, 1.5, 0.9)
        )
        dashboard = bpy.context.active_object
        dashboard.name = f"{name}_Dashboard"
        dashboard.scale = (1.8, 0.3, 0.4)
        bpy.ops.object.transform_apply(scale=True)
        dashboard.parent = parent
        interior_objects.append(dashboard)
        
        # Steering wheel
        bpy.ops.mesh.primitive_torus_add(
            major_radius=0.25, 
            minor_radius=0.03, 
            location=(-0.4, 1.2, 1.0)
        )
        steering_wheel = bpy.context.active_object
        steering_wheel.name = f"{name}_SteeringWheel"
        steering_wheel.rotation_euler = (math.pi/6, 0, 0)
        steering_wheel.parent = parent
        interior_objects.append(steering_wheel)
        
        # Seats
        seat_positions = [
            (-0.4, 0.5, 0.6, "DriverSeat"),
            (0.4, 0.5, 0.6, "PassengerSeat"),
            (-0.4, -0.8, 0.6, "RearSeatLeft"),
            (0.4, -0.8, 0.6, "RearSeatRight")
        ]
        
        for x, y, z, seat_name in seat_positions:
            bpy.ops.mesh.primitive_cube_add(
                size=1, 
                location=(x, y, z)
            )
            seat = bpy.context.active_object
            seat.name = f"{name}_Seat_{seat_name}"
            seat.scale = (0.4, 0.5, 0.4)
            bpy.ops.object.transform_apply(scale=True)
            seat.parent = parent
            interior_objects.append(seat)
        
        return interior_objects
    
    def _create_car_details(self, name, parent):
        """Create car details like lights, mirrors, etc."""
        details = []
        
        # Headlights
        light_positions = [
            (-0.7, 2.2, 0.8, "HeadlightLeft"),
            (0.7, 2.2, 0.8, "HeadlightRight")
        ]
        
        for x, y, z, light_name in light_positions:
            bpy.ops.mesh.primitive_uv_sphere_add(
                radius=0.15, 
                location=(x, y, z)
            )
            headlight = bpy.context.active_object
            headlight.name = f"{name}_{light_name}"
            headlight.parent = parent
            details.append(headlight)
        
        # Taillights
        tail_positions = [
            (-0.6, -2.2, 0.8, "TaillightLeft"),
            (0.6, -2.2, 0.8, "TaillightRight")
        ]
        
        for x, y, z, light_name in tail_positions:
            bpy.ops.mesh.primitive_cube_add(
                size=0.2, 
                location=(x, y, z)
            )
            taillight = bpy.context.active_object
            taillight.name = f"{name}_{light_name}"
            taillight.scale = (0.8, 0.3, 0.6)
            bpy.ops.object.transform_apply(scale=True)
            taillight.parent = parent
            details.append(taillight)
        
        # Side mirrors
        mirror_positions = [
            (-1.2, 1.5, 1.2, "MirrorLeft"),
            (1.2, 1.5, 1.2, "MirrorRight")
        ]
        
        for x, y, z, mirror_name in mirror_positions:
            bpy.ops.mesh.primitive_cube_add(
                size=0.1, 
                location=(x, y, z)
            )
            mirror = bpy.context.active_object
            mirror.name = f"{name}_{mirror_name}"
            mirror.scale = (0.5, 1, 0.8)
            bpy.ops.object.transform_apply(scale=True)
            mirror.parent = parent
            details.append(mirror)
        
        return details
    
    def _apply_car_materials(self, body, wheels, interior, details):
        """Apply appropriate materials to car parts"""
        # Get or create materials
        materials = self._get_car_materials()
        
        # Apply body material
        if not body.data.materials:
            body.data.materials.append(materials['CarPaint'])
        
        # Apply wheel materials
        for wheel in wheels:
            if 'Tire' in wheel.name:
                if not wheel.data.materials:
                    wheel.data.materials.append(materials['Tire'])
            elif 'Rim' in wheel.name:
                if not wheel.data.materials:
                    wheel.data.materials.append(materials['Chrome'])
        
        # Apply interior materials
        for obj in interior:
            if not obj.data.materials:
                if 'Dashboard' in obj.name:
                    obj.data.materials.append(materials['Plastic'])
                elif 'Seat' in obj.name:
                    obj.data.materials.append(materials['Fabric'])
                elif 'SteeringWheel' in obj.name:
                    obj.data.materials.append(materials['Leather'])
        
        # Apply detail materials
        for obj in details:
            if not obj.data.materials:
                if 'Headlight' in obj.name:
                    obj.data.materials.append(materials['Glass'])
                elif 'Taillight' in obj.name:
                    obj.data.materials.append(materials['TaillightRed'])
                elif 'Mirror' in obj.name:
                    obj.data.materials.append(materials['Chrome'])
    
    def _get_car_materials(self):
        """Get or create car materials"""
        materials = {}
        
        # Car paint material
        if 'CarPaint' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='CarPaint')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Red
            bsdf.inputs[6].default_value = 0.9  # Metallic
            bsdf.inputs[9].default_value = 0.1  # Roughness
        materials['CarPaint'] = bpy.data.materials['CarPaint']
        
        # Tire material
        if 'Tire' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Tire')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Dark
            bsdf.inputs[6].default_value = 0.0  # Metallic
            bsdf.inputs[9].default_value = 0.8  # Roughness
        materials['Tire'] = bpy.data.materials['Tire']
        
        # Chrome material
        if 'Chrome' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Chrome')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.9, 0.9, 0.9, 1.0)  # Bright
            bsdf.inputs[6].default_value = 1.0  # Metallic
            bsdf.inputs[9].default_value = 0.0  # Roughness
        materials['Chrome'] = bpy.data.materials['Chrome']
        
        # Glass material
        if 'Glass' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Glass')
            mat.use_nodes = True
            mat.blend_method = 'BLEND'
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.9, 0.9, 0.9, 1.0)
            bsdf.inputs[6].default_value = 0.0
            bsdf.inputs[9].default_value = 0.0
            bsdf.inputs[21].default_value = 0.3  # Alpha
        materials['Glass'] = bpy.data.materials['Glass']
        
        # Plastic material
        if 'Plastic' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Plastic')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)
            bsdf.inputs[6].default_value = 0.0
            bsdf.inputs[9].default_value = 0.3
        materials['Plastic'] = bpy.data.materials['Plastic']
        
        # Fabric material
        if 'Fabric' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Fabric')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.3, 0.3, 0.4, 1.0)
            bsdf.inputs[6].default_value = 0.0
            bsdf.inputs[9].default_value = 0.7
        materials['Fabric'] = bpy.data.materials['Fabric']
        
        # Leather material
        if 'Leather' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='Leather')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.2, 0.15, 0.1, 1.0)
            bsdf.inputs[6].default_value = 0.0
            bsdf.inputs[9].default_value = 0.4
        materials['Leather'] = bpy.data.materials['Leather']
        
        # Taillight red material
        if 'TaillightRed' not in bpy.data.materials:
            mat = bpy.data.materials.new(name='TaillightRed')
            mat.use_nodes = True
            bsdf = mat.node_tree.nodes["Principled BSDF"]
            bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)
            bsdf.inputs[6].default_value = 0.0
            bsdf.inputs[9].default_value = 0.2
            bsdf.inputs[19].default_value = 0.5  # Emission
        materials['TaillightRed'] = bpy.data.materials['TaillightRed']
        
        return materials

# Main execution function
def create_vehicle_collection():
    """Create a collection of vehicles for the driving simulator"""
    print("🚗 Creating Vehicle Collection for Driving Simulator")
    print("=" * 60)
    
    creator = DetailedVehicleCreator()
    
    # Create different vehicle types
    vehicles = []
    
    # Sedan
    sedan = creator.create_sedan("Sedan_01")
    vehicles.append(sedan)
    
    # SUV (scaled up sedan)
    suv = creator.create_sedan("SUV_01")
    suv.scale = (1.2, 1.1, 1.3)
    bpy.ops.object.transform_apply(scale=True)
    vehicles.append(suv)
    
    # Sports car (scaled down, wider)
    sports = creator.create_sedan("SportsCar_01")
    sports.scale = (1.1, 0.9, 0.8)
    bpy.ops.object.transform_apply(scale=True)
    vehicles.append(sports)
    
    print("=" * 60)
    print("✅ Vehicle collection created!")
    print(f"Created {len(vehicles)} vehicles:")
    for vehicle in vehicles:
        print(f"  - {vehicle.name}")
    
    print("\nNext steps:")
    print("1. Refine vehicle shapes in Edit mode")
    print("2. Add more detail (door handles, grilles, etc.)")
    print("3. UV unwrap for custom textures")
    print("4. Export to Unity using the export script")
    
    return vehicles

if __name__ == "__main__":
    create_vehicle_collection()
