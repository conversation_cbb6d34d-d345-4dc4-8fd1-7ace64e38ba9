# GameManager.gd - FREE Game Management System
# Handles missions, money, progression for the FREE Driving Simulator

extends Node
class_name GameManager

# Game state
enum GameState {
	MAIN_MENU,
	MISSION_BRIEFING,
	DRIVING,
	MISSION_COMPLETE,
	MISSION_FAILED,
	PAUSED,
	SHOP
}

var current_state: GameState = GameState.MAIN_MENU

# Player stats
@export var player_money: float = 1000.0
@export var missions_completed: int = 0
@export var missions_failed: int = 0
@export var total_distance_driven: float = 0.0

# Current mission
var current_mission: Mission
var mission_start_time: float
var mission_time_limit: float

# References
var player_car: CarController
var ui_manager: UIManager
var map_loader: MapLoader

# Signals
signal game_state_changed(new_state: GameState)
signal money_changed(new_amount: float)
signal mission_started(mission: Mission)
signal mission_completed(mission: Mission, reward: float)
signal mission_failed(mission: Mission, reason: String)

class Mission:
	var id: String
	var title: String
	var description: String
	var start_location: Vector3
	var end_location: Vector3
	var start_name: String
	var end_name: String
	var distance_km: float
	var time_limit_minutes: float
	var base_reward: float
	var fuel_reward: float
	var difficulty: int  # 1-5
	
	func _init():
		id = "mission_" + str(Time.get_unix_time_from_system())

func _ready():
	print("🎮 FREE Game Manager initialized")
	setup_game()

func setup_game():
	"""Initialize the game systems"""
	# Find player car
	player_car = get_tree().get_first_node_in_group("player_car")
	if player_car:
		player_car.speed_changed.connect(_on_speed_changed)
		player_car.fuel_changed.connect(_on_fuel_changed)
		player_car.damage_changed.connect(_on_damage_changed)
		print("✅ Player car connected")
	
	# Find UI manager
	ui_manager = get_tree().get_first_node_in_group("ui_manager")
	if ui_manager:
		ui_manager.mission_start_requested.connect(_on_mission_start_requested)
		ui_manager.shop_item_purchased.connect(_on_shop_item_purchased)
		print("✅ UI manager connected")
	
	# Find map loader
	map_loader = get_tree().get_first_node_in_group("map_loader")
	if map_loader:
		print("✅ Map loader connected")
	
	# Set initial state
	change_game_state(GameState.MAIN_MENU)

func change_game_state(new_state: GameState):
	"""Change the current game state"""
	var old_state = current_state
	current_state = new_state
	
	print("🔄 Game state: %s → %s" % [GameState.keys()[old_state], GameState.keys()[new_state]])
	
	# Handle state transitions
	match new_state:
		GameState.MAIN_MENU:
			get_tree().paused = false
			if ui_manager:
				ui_manager.show_main_menu()
		
		GameState.MISSION_BRIEFING:
			if ui_manager:
				ui_manager.show_mission_briefing(current_mission)
		
		GameState.DRIVING:
			get_tree().paused = false
			mission_start_time = Time.get_time_dict_from_system()["unix"]
			if ui_manager:
				ui_manager.show_driving_hud()
		
		GameState.MISSION_COMPLETE:
			get_tree().paused = true
			if ui_manager:
				ui_manager.show_mission_complete()
		
		GameState.MISSION_FAILED:
			get_tree().paused = true
			if ui_manager:
				ui_manager.show_mission_failed()
		
		GameState.PAUSED:
			get_tree().paused = true
			if ui_manager:
				ui_manager.show_pause_menu()
		
		GameState.SHOP:
			get_tree().paused = true
			if ui_manager:
				ui_manager.show_shop()
	
	game_state_changed.emit(new_state)

func create_random_mission() -> Mission:
	"""Create a random mission using free map data"""
	var mission = Mission.new()
	
	# Random mission types
	var mission_types = [
		"Delivery",
		"Passenger Transport", 
		"Emergency Response",
		"Sightseeing Tour",
		"Fuel Run"
	]
	
	var type = mission_types[randi() % mission_types.size()]
	
	# Generate locations (using free city data)
	var locations = get_free_locations()
	var start_loc = locations[randi() % locations.size()]
	var end_loc = locations[randi() % locations.size()]
	
	while end_loc == start_loc:
		end_loc = locations[randi() % locations.size()]
	
	mission.title = type + " Mission"
	mission.start_name = start_loc.name
	mission.end_name = end_loc.name
	mission.start_location = start_loc.position
	mission.end_location = end_loc.position
	
	# Calculate distance and time
	mission.distance_km = mission.start_location.distance_to(mission.end_location) / 1000.0
	mission.time_limit_minutes = mission.distance_km * 2.0 + randf_range(5.0, 15.0)
	
	# Calculate rewards
	mission.base_reward = mission.distance_km * 10.0 + randf_range(50.0, 200.0)
	mission.fuel_reward = mission.distance_km * 0.5
	
	# Set difficulty
	mission.difficulty = clampi(int(mission.distance_km / 10.0), 1, 5)
	
	# Create description
	mission.description = "Drive from %s to %s\nDistance: %.1f km\nTime Limit: %.0f minutes\nReward: $%.0f" % [
		mission.start_name,
		mission.end_name, 
		mission.distance_km,
		mission.time_limit_minutes,
		mission.base_reward
	]
	
	print("🎯 Created mission: %s (%.1f km, $%.0f)" % [mission.title, mission.distance_km, mission.base_reward])
	return mission

func get_free_locations() -> Array:
	"""Get free location data (no API costs)"""
	# This would normally load from OpenStreetMap data
	# For now, using sample locations
	return [
		{"name": "Downtown Plaza", "position": Vector3(0, 0, 0)},
		{"name": "Shopping Mall", "position": Vector3(2000, 0, 1500)},
		{"name": "Airport", "position": Vector3(-3000, 0, -2000)},
		{"name": "University", "position": Vector3(1500, 0, -1000)},
		{"name": "Hospital", "position": Vector3(-1000, 0, 2000)},
		{"name": "Train Station", "position": Vector3(500, 0, -1500)},
		{"name": "Beach Resort", "position": Vector3(4000, 0, 3000)},
		{"name": "Mountain Lodge", "position": Vector3(-2500, 0, 4000)},
		{"name": "Industrial District", "position": Vector3(3500, 0, -2500)},
		{"name": "Residential Area", "position": Vector3(-1500, 0, -3000)}
	]

func start_mission(mission: Mission):
	"""Start a new mission"""
	current_mission = mission
	mission_time_limit = mission.time_limit_minutes * 60.0  # Convert to seconds
	
	# Position player at start location
	if player_car:
		player_car.global_position = mission.start_location
		player_car.linear_velocity = Vector3.ZERO
		player_car.angular_velocity = Vector3.ZERO
	
	change_game_state(GameState.DRIVING)
	mission_started.emit(mission)
	
	print("🚀 Mission started: %s" % mission.title)

func _process(delta):
	"""Update game logic"""
	if current_state == GameState.DRIVING and current_mission:
		update_mission_progress()

func update_mission_progress():
	"""Check mission progress and completion"""
	if not player_car or not current_mission:
		return
	
	# Check if reached destination
	var distance_to_target = player_car.global_position.distance_to(current_mission.end_location)
	
	if distance_to_target < 50.0:  # Within 50 meters
		complete_mission()
		return
	
	# Check time limit
	var current_time = Time.get_time_dict_from_system()["unix"]
	var elapsed_time = current_time - mission_start_time
	
	if elapsed_time >= mission_time_limit:
		fail_mission("Time limit exceeded!")
		return
	
	# Check fuel
	if player_car.current_fuel <= 0:
		fail_mission("Out of fuel!")
		return
	
	# Check damage
	if player_car.current_damage >= player_car.max_damage:
		fail_mission("Vehicle too damaged!")
		return

func complete_mission():
	"""Complete the current mission"""
	if not current_mission:
		return
	
	# Calculate performance bonuses
	var current_time = Time.get_time_dict_from_system()["unix"]
	var elapsed_time = current_time - mission_start_time
	var time_bonus = max(0, (mission_time_limit - elapsed_time) / mission_time_limit) * 0.5
	
	var fuel_bonus = player_car.get_fuel_percentage() * 0.3
	var damage_bonus = (1.0 - player_car.get_damage_percentage()) * 0.2
	
	# Calculate final reward
	var total_multiplier = 1.0 + time_bonus + fuel_bonus + damage_bonus
	var final_reward = current_mission.base_reward * total_multiplier
	
	# Award money and fuel
	add_money(final_reward)
	player_car.add_fuel(current_mission.fuel_reward)
	
	missions_completed += 1
	
	print("🎉 Mission completed! Reward: $%.0f (%.0f%% bonus)" % [final_reward, (total_multiplier - 1.0) * 100])
	
	mission_completed.emit(current_mission, final_reward)
	change_game_state(GameState.MISSION_COMPLETE)

func fail_mission(reason: String):
	"""Fail the current mission"""
	missions_failed += 1
	
	print("❌ Mission failed: %s" % reason)
	
	mission_failed.emit(current_mission, reason)
	change_game_state(GameState.MISSION_FAILED)

func add_money(amount: float):
	"""Add money to player account"""
	player_money += amount
	money_changed.emit(player_money)
	print("💰 Money: $%.0f (+$%.0f)" % [player_money, amount])

func spend_money(amount: float) -> bool:
	"""Spend money if player has enough"""
	if player_money >= amount:
		player_money -= amount
		money_changed.emit(player_money)
		print("💸 Money: $%.0f (-$%.0f)" % [player_money, amount])
		return true
	else:
		print("❌ Not enough money! Need $%.0f, have $%.0f" % [amount, player_money])
		return false

# Signal handlers
func _on_mission_start_requested(start_location: String, end_location: String):
	"""Handle mission start request from UI"""
	var mission = create_random_mission()
	current_mission = mission
	change_game_state(GameState.MISSION_BRIEFING)

func _on_shop_item_purchased(item_type: String, cost: float):
	"""Handle shop purchases"""
	if not spend_money(cost):
		return
	
	match item_type:
		"fuel_10L":
			player_car.add_fuel(10.0)
		"fuel_full":
			player_car.add_fuel(player_car.max_fuel)
		"repair_minor":
			player_car.repair_vehicle(25.0)
		"repair_major":
			player_car.repair_vehicle(50.0)
		"repair_full":
			player_car.repair_vehicle(player_car.max_damage)

func _on_speed_changed(speed: float):
	"""Handle speed updates from car"""
	if ui_manager:
		ui_manager.update_speed_display(speed)

func _on_fuel_changed(fuel_percentage: float):
	"""Handle fuel updates from car"""
	if ui_manager:
		ui_manager.update_fuel_display(fuel_percentage)

func _on_damage_changed(damage_percentage: float):
	"""Handle damage updates from car"""
	if ui_manager:
		ui_manager.update_damage_display(damage_percentage)

# Input handling
func _input(event):
	"""Handle global input"""
	if event.is_action_pressed("pause") and current_state == GameState.DRIVING:
		change_game_state(GameState.PAUSED)
	elif event.is_action_pressed("pause") and current_state == GameState.PAUSED:
		change_game_state(GameState.DRIVING)

# Save/Load system (FREE - no cloud costs)
func save_game():
	"""Save game progress to local file"""
	var save_data = {
		"player_money": player_money,
		"missions_completed": missions_completed,
		"missions_failed": missions_failed,
		"total_distance": total_distance_driven
	}
	
	var file = FileAccess.open("user://savegame.dat", FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(save_data))
		file.close()
		print("💾 Game saved")

func load_game():
	"""Load game progress from local file"""
	var file = FileAccess.open("user://savegame.dat", FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var save_data = json.data
			player_money = save_data.get("player_money", 1000.0)
			missions_completed = save_data.get("missions_completed", 0)
			missions_failed = save_data.get("missions_failed", 0)
			total_distance_driven = save_data.get("total_distance", 0.0)
			
			money_changed.emit(player_money)
			print("📁 Game loaded")

# Getters for UI
func get_player_money() -> float:
	return player_money

func get_missions_completed() -> int:
	return missions_completed

func get_missions_failed() -> int:
	return missions_failed

func get_success_rate() -> float:
	var total = missions_completed + missions_failed
	if total == 0:
		return 0.0
	return float(missions_completed) / float(total) * 100.0
