@echo off
title Realistic Driving Simulator - Quick Start
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                                                              ║
echo  ║           🚗 REALISTIC DRIVING SIMULATOR 🚗                  ║
echo  ║                                                              ║
echo  ║                    Quick Start Guide                         ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 Project Location: %~dp0
echo.

echo 🔍 Checking system requirements...
echo.

REM Check if Unity Hub is installed
set "UNITY_HUB_FOUND=0"
if exist "C:\Program Files\Unity Hub\Unity Hub.exe" (
    set "UNITY_HUB_FOUND=1"
    set "UNITY_HUB_PATH=C:\Program Files\Unity Hub\Unity Hub.exe"
    echo ✅ Unity Hub found at: C:\Program Files\Unity Hub\
) else if exist "C:\Users\<USER>\AppData\Local\Programs\Unity Hub\Unity Hub.exe" (
    set "UNITY_HUB_FOUND=1"
    set "UNITY_HUB_PATH=C:\Users\<USER>\AppData\Local\Programs\Unity Hub\Unity Hub.exe"
    echo ✅ Unity Hub found at: %USERPROFILE%\AppData\Local\Programs\Unity Hub\
) else (
    echo ❌ Unity Hub not found
)

REM Check internet connection
ping -n 1 google.com >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Internet connection available
) else (
    echo ❌ No internet connection detected
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo.

if %UNITY_HUB_FOUND% == 1 (
    echo 🎉 Great! Unity Hub is installed. Let's get started!
    echo.
    echo What would you like to do?
    echo.
    echo [1] Open Unity Hub and add this project
    echo [2] Set up Blender for 3D assets (recommended)
    echo [3] View installation checklist
    echo [4] Open Google Cloud Console for API keys
    echo [5] View project documentation
    echo [6] Exit
    echo.
    set /p choice="Enter your choice (1-6): "

    if "%choice%"=="1" goto open_unity
    if "%choice%"=="2" goto setup_blender
    if "%choice%"=="3" goto show_checklist
    if "%choice%"=="4" goto open_google_console
    if "%choice%"=="5" goto show_docs
    if "%choice%"=="6" goto end

    echo Invalid choice. Opening Unity Hub...
    goto open_unity

) else (
    echo 📥 Unity Hub is not installed. Let's install it first!
    echo.
    echo What would you like to do?
    echo.
    echo [1] Download Unity Hub (opens browser)
    echo [2] View installation guide
    echo [3] Exit
    echo.
    set /p choice="Enter your choice (1-3): "

    if "%choice%"=="1" goto download_unity
    if "%choice%"=="2" goto show_guide
    if "%choice%"=="3" goto end

    echo Invalid choice. Opening download page...
    goto download_unity
)

:open_unity
echo.
echo 🚀 Opening Unity Hub...
echo.
echo Instructions:
echo 1. In Unity Hub, click "Add" button
echo 2. Navigate to this folder: %~dp0
echo 3. Select the folder and click "Add Project"
echo 4. Click on the project to open it
echo 5. Wait for Unity to import assets (first time takes 5-10 minutes)
echo.
start "" "%UNITY_HUB_PATH%"
echo Unity Hub launched! Follow the instructions above.
goto end

:setup_blender
echo.
echo 🎨 Setting up Blender for 3D asset creation...
echo.
echo Blender will provide professional-quality 3D models for:
echo ✅ Realistic vehicles (cars, trucks, motorcycles)
echo ✅ Detailed buildings (gas stations, shops, houses)
echo ✅ Environment props (trees, signs, furniture)
echo ✅ Custom materials and textures
echo.
call SetupBlender.bat
goto end

:download_unity
echo.
echo 🌐 Opening Unity download page...
start https://unity3d.com/get-unity/download
echo.
echo After downloading and installing Unity Hub:
echo 1. Install Unity 2023.3 LTS
echo 2. Run this script again
echo 3. Follow the setup instructions
goto end

:show_checklist
echo.
echo 📋 Opening installation checklist...
if exist "INSTALLATION_CHECKLIST.md" (
    start notepad "INSTALLATION_CHECKLIST.md"
) else (
    echo INSTALLATION_CHECKLIST.md not found in project directory
)
goto end

:open_google_console
echo.
echo 🗺️ Opening Google Cloud Console for API setup...
start https://console.cloud.google.com/
echo.
echo Instructions for Google Maps API:
echo 1. Create a new project or select existing
echo 2. Enable these APIs:
echo    - Maps JavaScript API
echo    - Directions API
echo    - Places API (New)
echo    - Geocoding API
echo 3. Create API Key in Credentials section
echo 4. Copy the API key for later use in Unity
goto end

:show_docs
echo.
echo 📚 Available documentation:
echo.
if exist "README.md" echo ✅ README.md - Project overview
if exist "SETUP_GUIDE.md" echo ✅ SETUP_GUIDE.md - Detailed setup instructions
if exist "INSTALLATION_CHECKLIST.md" echo ✅ INSTALLATION_CHECKLIST.md - Step-by-step checklist
if exist "DEVELOPMENT_ROADMAP.md" echo ✅ DEVELOPMENT_ROADMAP.md - Future development plans
echo.
echo Opening README.md...
if exist "README.md" (
    start notepad "README.md"
) else (
    echo README.md not found
)
goto end

:show_guide
echo.
echo 📖 QUICK INSTALLATION GUIDE:
echo ═══════════════════════════════════════════════════════════════
echo.
echo Step 1: Install Unity Hub
echo   • Download from: https://unity3d.com/get-unity/download
echo   • Run the installer and follow instructions
echo.
echo Step 2: Install Unity Editor
echo   • Open Unity Hub
echo   • Go to "Installs" tab
echo   • Install Unity 2023.3 LTS
echo   • Include Windows Build Support
echo.
echo Step 3: Get Google Maps API Key
echo   • Go to: https://console.cloud.google.com/
echo   • Create project and enable Maps APIs
echo   • Create API key and save it
echo.
echo Step 4: Open Project
echo   • Run this script again after Unity installation
echo   • Add project to Unity Hub
echo   • Configure API keys in Unity
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
echo Press any key to open Unity download page...
pause >nul
start https://unity3d.com/get-unity/download
goto end

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🎯 Next Steps Summary:
echo.
if %UNITY_HUB_FOUND% == 1 (
    echo ✅ Unity Hub is ready
    echo 📁 Add this project to Unity Hub: %~dp0
    echo 🔑 Get Google Maps API key from Google Cloud Console
    echo 🎮 Open project in Unity and start developing!
) else (
    echo 📥 Install Unity Hub first
    echo 🔄 Run this script again after installation
    echo 📖 Check INSTALLATION_CHECKLIST.md for detailed steps
)
echo.
echo 📞 Need help? Check the documentation files in this folder
echo.
echo Press any key to exit...
pause >nul
