using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Complete Dr. Driving Car Controller
/// Authentic recreation of Dr. Driving vehicle physics and controls
/// </summary>
public class CarController : MonoBehaviour
{
    [Header("Dr. Driving Vehicle Settings")]
    [SerializeField] private float motorForce = 1500f;
    [SerializeField] private float brakeForce = 3000f;
    [SerializeField] private float maxSteerAngle = 30f;
    [SerializeField] private float maxSpeed = 200f; // km/h
    
    [Header("Wheel Colliders (Dr. Driving Style)")]
    [SerializeField] private WheelCollider frontLeftWheelCollider;
    [SerializeField] private WheelCollider frontRightWheelCollider;
    [SerializeField] private WheelCollider rearLeftWheelCollider;
    [SerializeField] private WheelCollider rearRightWheelCollider;
    
    [Header("Wheel Transforms (Visual)")]
    [SerializeField] private Transform frontLeftWheelTransform;
    [SerializeField] private Transform frontRightWheelTransform;
    [SerializeField] private Transform rearLeftWheelTransform;
    [SerializeField] private Transform rearRightWheelTransform;
    
    [Header("Dr. Driving Audio")]
    [SerializeField] private AudioSource engineAudioSource;
    [SerializeField] private AudioSource brakeAudioSource;
    [SerializeField] private AudioSource hornAudioSource;
    [SerializeField] private AudioClip engineIdleClip;
    [SerializeField] private AudioClip engineRevClip;
    [SerializeField] private AudioClip brakeClip;
    [SerializeField] private AudioClip hornClip;
    
    [Header("Dr. Driving Effects")]
    [SerializeField] private ParticleSystem exhaustParticles;
    [SerializeField] private ParticleSystem dustParticles;
    [SerializeField] private Light[] headlights;
    [SerializeField] private Light[] brakelights;
    [SerializeField] private Light[] turnSignalLights;
    
    [Header("Dr. Driving UI")]
    [SerializeField] private Slider speedometer;
    [SerializeField] private Text speedText;
    [SerializeField] private Slider fuelGauge;
    [SerializeField] private Text gearText;
    [SerializeField] private Image turnSignalUI;
    
    // Private variables
    private float horizontalInput;
    private float verticalInput;
    private float currentSteerAngle;
    private bool isBraking;
    private bool isReversing;
    private Rigidbody carRigidbody;
    
    // Dr. Driving specific features
    private float currentSpeed;
    private float currentFuel = 100f;
    private int currentGear = 1;
    private bool leftTurnSignal = false;
    private bool rightTurnSignal = false;
    private bool headlightsOn = false;
    private float engineRPM;
    
    // Mobile controls (Dr. Driving style)
    [Header("Mobile Controls")]
    public Button gasButton;
    public Button brakeButton;
    public Button leftTurnButton;
    public Button rightTurnButton;
    public Button hornButton;
    public Button headlightButton;
    public Slider steeringSlider;
    
    void Start()
    {
        carRigidbody = GetComponent<Rigidbody>();
        carRigidbody.centerOfMass = new Vector3(0, -0.5f, 0.5f); // Lower center of mass for stability
        
        SetupMobileControls();
        SetupAudio();
        
        Debug.Log("🚗 Dr. Driving Car Controller initialized");
    }
    
    void Update()
    {
        GetInput();
        HandleMotor();
        HandleSteering();
        UpdateWheelPoses();
        UpdateAudio();
        UpdateEffects();
        UpdateUI();
        UpdateDrDrivingFeatures();
    }
    
    void GetInput()
    {
        // Desktop input
        horizontalInput = Input.GetAxis("Horizontal");
        verticalInput = Input.GetAxis("Vertical");
        isBraking = Input.GetKey(KeyCode.Space);
        
        // Dr. Driving mobile controls
        if (gasButton != null && gasButton.GetComponent<Button>().interactable)
        {
            // Mobile gas input handled by button events
        }
        
        // Turn signals (Dr. Driving feature)
        if (Input.GetKeyDown(KeyCode.Q)) ToggleLeftTurnSignal();
        if (Input.GetKeyDown(KeyCode.E)) ToggleRightTurnSignal();
        if (Input.GetKeyDown(KeyCode.H)) SoundHorn();
        if (Input.GetKeyDown(KeyCode.L)) ToggleHeadlights();
    }
    
    void HandleMotor()
    {
        // Calculate current speed in km/h
        currentSpeed = carRigidbody.velocity.magnitude * 3.6f;
        
        // Speed limiter (Dr. Driving style)
        if (currentSpeed >= maxSpeed && verticalInput > 0)
        {
            verticalInput = 0;
        }
        
        // Apply motor force to rear wheels
        frontLeftWheelCollider.motorTorque = verticalInput * motorForce;
        frontRightWheelCollider.motorTorque = verticalInput * motorForce;
        
        // Apply braking force
        currentBrakeForce = isBraking ? brakeForce : 0f;
        ApplyBraking();
        
        // Fuel consumption (Dr. Driving feature)
        if (verticalInput > 0)
        {
            currentFuel -= Time.deltaTime * 0.5f; // Consume fuel when accelerating
            currentFuel = Mathf.Max(0, currentFuel);
        }
        
        // Engine RPM calculation
        engineRPM = Mathf.Lerp(800, 6000, Mathf.Abs(verticalInput));
    }
    
    private float currentBrakeForce;
    
    void ApplyBraking()
    {
        frontLeftWheelCollider.brakeTorque = currentBrakeForce;
        frontRightWheelCollider.brakeTorque = currentBrakeForce;
        rearLeftWheelCollider.brakeTorque = currentBrakeForce;
        rearRightWheelCollider.brakeTorque = currentBrakeForce;
    }
    
    void HandleSteering()
    {
        currentSteerAngle = maxSteerAngle * horizontalInput;
        frontLeftWheelCollider.steerAngle = currentSteerAngle;
        frontRightWheelCollider.steerAngle = currentSteerAngle;
    }
    
    void UpdateWheelPoses()
    {
        UpdateWheelPose(frontLeftWheelCollider, frontLeftWheelTransform);
        UpdateWheelPose(frontRightWheelCollider, frontRightWheelTransform);
        UpdateWheelPose(rearLeftWheelCollider, rearLeftWheelTransform);
        UpdateWheelPose(rearRightWheelCollider, rearRightWheelTransform);
    }
    
    void UpdateWheelPose(WheelCollider collider, Transform wheelTransform)
    {
        Vector3 pos;
        Quaternion rot;
        collider.GetWorldPose(out pos, out rot);
        wheelTransform.position = pos;
        wheelTransform.rotation = rot;
    }
    
    void UpdateAudio()
    {
        if (engineAudioSource != null)
        {
            // Engine sound based on RPM (Dr. Driving style)
            engineAudioSource.pitch = Mathf.Lerp(0.8f, 2.0f, engineRPM / 6000f);
            engineAudioSource.volume = Mathf.Lerp(0.3f, 1.0f, Mathf.Abs(verticalInput));
        }
        
        // Brake sound
        if (brakeAudioSource != null && isBraking && currentSpeed > 10f)
        {
            if (!brakeAudioSource.isPlaying)
            {
                brakeAudioSource.clip = brakeClip;
                brakeAudioSource.Play();
            }
        }
        else if (brakeAudioSource != null)
        {
            brakeAudioSource.Stop();
        }
    }
    
    void UpdateEffects()
    {
        // Exhaust particles
        if (exhaustParticles != null)
        {
            var emission = exhaustParticles.emission;
            emission.rateOverTime = Mathf.Lerp(10, 50, Mathf.Abs(verticalInput));
        }
        
        // Dust particles when driving
        if (dustParticles != null && currentSpeed > 5f)
        {
            if (!dustParticles.isPlaying)
                dustParticles.Play();
        }
        else if (dustParticles != null)
        {
            dustParticles.Stop();
        }
        
        // Brake lights
        foreach (Light brakelight in brakelights)
        {
            brakelight.enabled = isBraking;
        }
        
        // Headlights
        foreach (Light headlight in headlights)
        {
            headlight.enabled = headlightsOn;
        }
    }
    
    void UpdateUI()
    {
        // Speedometer (Dr. Driving style)
        if (speedometer != null)
        {
            speedometer.value = currentSpeed / maxSpeed;
        }
        
        if (speedText != null)
        {
            speedText.text = Mathf.RoundToInt(currentSpeed) + " km/h";
        }
        
        // Fuel gauge
        if (fuelGauge != null)
        {
            fuelGauge.value = currentFuel / 100f;
        }
        
        // Gear indicator
        if (gearText != null)
        {
            if (verticalInput < -0.1f)
                gearText.text = "R";
            else if (currentSpeed < 10f)
                gearText.text = "1";
            else if (currentSpeed < 30f)
                gearText.text = "2";
            else if (currentSpeed < 60f)
                gearText.text = "3";
            else
                gearText.text = "4";
        }
    }
    
    void UpdateDrDrivingFeatures()
    {
        // Turn signal blinking
        if (leftTurnSignal || rightTurnSignal)
        {
            float blinkRate = Mathf.Sin(Time.time * 6f);
            
            if (leftTurnSignal && turnSignalLights.Length > 0)
            {
                turnSignalLights[0].enabled = blinkRate > 0;
            }
            
            if (rightTurnSignal && turnSignalLights.Length > 1)
            {
                turnSignalLights[1].enabled = blinkRate > 0;
            }
        }
        
        // Auto turn off turn signals when steering opposite
        if (leftTurnSignal && horizontalInput > 0.5f)
            leftTurnSignal = false;
        if (rightTurnSignal && horizontalInput < -0.5f)
            rightTurnSignal = false;
    }
    
    void SetupMobileControls()
    {
        // Gas button
        if (gasButton != null)
        {
            gasButton.onClick.AddListener(() => verticalInput = 1f);
        }
        
        // Brake button
        if (brakeButton != null)
        {
            brakeButton.onClick.AddListener(() => isBraking = true);
        }
        
        // Turn signal buttons
        if (leftTurnButton != null)
        {
            leftTurnButton.onClick.AddListener(ToggleLeftTurnSignal);
        }
        
        if (rightTurnButton != null)
        {
            rightTurnButton.onClick.AddListener(ToggleRightTurnSignal);
        }
        
        // Horn button
        if (hornButton != null)
        {
            hornButton.onClick.AddListener(SoundHorn);
        }
        
        // Headlight button
        if (headlightButton != null)
        {
            headlightButton.onClick.AddListener(ToggleHeadlights);
        }
        
        // Steering slider
        if (steeringSlider != null)
        {
            steeringSlider.onValueChanged.AddListener((value) => {
                horizontalInput = (value - 0.5f) * 2f; // Convert 0-1 to -1 to 1
            });
        }
    }
    
    void SetupAudio()
    {
        if (engineAudioSource != null && engineIdleClip != null)
        {
            engineAudioSource.clip = engineIdleClip;
            engineAudioSource.loop = true;
            engineAudioSource.Play();
        }
    }
    
    // Dr. Driving specific methods
    public void ToggleLeftTurnSignal()
    {
        leftTurnSignal = !leftTurnSignal;
        rightTurnSignal = false; // Turn off right signal
        Debug.Log("Left turn signal: " + leftTurnSignal);
    }
    
    public void ToggleRightTurnSignal()
    {
        rightTurnSignal = !rightTurnSignal;
        leftTurnSignal = false; // Turn off left signal
        Debug.Log("Right turn signal: " + rightTurnSignal);
    }
    
    public void SoundHorn()
    {
        if (hornAudioSource != null && hornClip != null)
        {
            hornAudioSource.PlayOneShot(hornClip);
            Debug.Log("🔊 Horn sounded");
        }
    }
    
    public void ToggleHeadlights()
    {
        headlightsOn = !headlightsOn;
        Debug.Log("Headlights: " + (headlightsOn ? "ON" : "OFF"));
    }
    
    // Public getters for other scripts
    public float GetCurrentSpeed() => currentSpeed;
    public float GetCurrentFuel() => currentFuel;
    public int GetCurrentGear() => currentGear;
    public bool IsMoving() => currentSpeed > 1f;
    public bool IsBraking() => isBraking;
    
    // Fuel management
    public void AddFuel(float amount)
    {
        currentFuel = Mathf.Min(100f, currentFuel + amount);
        Debug.Log($"⛽ Fuel added: {amount}. Current fuel: {currentFuel}%");
    }
    
    // Damage system (Dr. Driving feature)
    void OnCollisionEnter(Collision collision)
    {
        if (collision.relativeVelocity.magnitude > 5f)
        {
            // Handle collision damage
            float damage = collision.relativeVelocity.magnitude * 2f;
            Debug.Log($"💥 Collision! Damage: {damage}");
            
            // Reduce performance based on damage
            motorForce *= 0.95f;
            maxSpeed *= 0.98f;
        }
    }
}
