# UIManager.gd - FREE UI System for Driving Simulator
# Manages all user interface elements

extends Control
class_name UIManager

# UI Panels
@onready var main_menu_panel: Control = $MainMenuPanel
@onready var mission_briefing_panel: Control = $MissionBriefingPanel
@onready var driving_hud_panel: Control = $DrivingHUDPanel
@onready var mission_complete_panel: Control = $MissionCompletePanel
@onready var mission_failed_panel: Control = $MissionFailedPanel
@onready var pause_menu_panel: Control = $PauseMenuPanel
@onready var shop_panel: Control = $ShopPanel

# HUD Elements
@onready var speed_label: Label = $DrivingHUDPanel/SpeedLabel
@onready var fuel_bar: ProgressBar = $DrivingHUDPanel/FuelBar
@onready var damage_bar: ProgressBar = $DrivingHUDPanel/DamageBar
@onready var timer_label: Label = $DrivingHUDPanel/TimerLabel
@onready var money_label: Label = $DrivingHUDPanel/MoneyLabel
@onready var gps_label: Label = $DrivingHUDPanel/GPSLabel

# Mission UI
@onready var mission_description: Label = $MissionBriefingPanel/MissionDescription
@onready var start_mission_button: Button = $MissionBriefingPanel/StartMissionButton
@onready var cancel_mission_button: Button = $MissionBriefingPanel/CancelMissionButton

# Main Menu
@onready var new_mission_button: Button = $MainMenuPanel/NewMissionButton
@onready var continue_button: Button = $MainMenuPanel/ContinueButton
@onready var settings_button: Button = $MainMenuPanel/SettingsButton
@onready var quit_button: Button = $MainMenuPanel/QuitButton

# Signals
signal mission_start_requested(start_location: String, end_location: String)
signal shop_item_purchased(item_type: String, cost: float)

func _ready():
	print("🎮 FREE UI Manager initialized")
	setup_ui()
	connect_signals()

func setup_ui():
	"""Initialize UI elements"""
	# Hide all panels initially
	hide_all_panels()
	
	# Show main menu
	show_main_menu()
	
	# Set initial values
	update_speed_display(0.0)
	update_fuel_display(1.0)
	update_damage_display(0.0)
	update_money_display(1000.0)

func connect_signals():
	"""Connect UI button signals"""
	if new_mission_button:
		new_mission_button.pressed.connect(_on_new_mission_pressed)
	
	if continue_button:
		continue_button.pressed.connect(_on_continue_pressed)
	
	if settings_button:
		settings_button.pressed.connect(_on_settings_pressed)
	
	if quit_button:
		quit_button.pressed.connect(_on_quit_pressed)
	
	if start_mission_button:
		start_mission_button.pressed.connect(_on_start_mission_pressed)
	
	if cancel_mission_button:
		cancel_mission_button.pressed.connect(_on_cancel_mission_pressed)

func hide_all_panels():
	"""Hide all UI panels"""
	if main_menu_panel: main_menu_panel.visible = false
	if mission_briefing_panel: mission_briefing_panel.visible = false
	if driving_hud_panel: driving_hud_panel.visible = false
	if mission_complete_panel: mission_complete_panel.visible = false
	if mission_failed_panel: mission_failed_panel.visible = false
	if pause_menu_panel: pause_menu_panel.visible = false
	if shop_panel: shop_panel.visible = false

func show_main_menu():
	"""Show the main menu"""
	hide_all_panels()
	if main_menu_panel:
		main_menu_panel.visible = true
	print("📱 Showing main menu")

func show_mission_briefing(mission = null):
	"""Show mission briefing screen"""
	hide_all_panels()
	if mission_briefing_panel:
		mission_briefing_panel.visible = true
	
	if mission and mission_description:
		mission_description.text = mission.description
	
	print("📋 Showing mission briefing")

func show_driving_hud():
	"""Show the driving HUD"""
	hide_all_panels()
	if driving_hud_panel:
		driving_hud_panel.visible = true
	print("🚗 Showing driving HUD")

func show_mission_complete():
	"""Show mission complete screen"""
	hide_all_panels()
	if mission_complete_panel:
		mission_complete_panel.visible = true
	print("🎉 Showing mission complete")

func show_mission_failed():
	"""Show mission failed screen"""
	hide_all_panels()
	if mission_failed_panel:
		mission_failed_panel.visible = true
	print("❌ Showing mission failed")

func show_pause_menu():
	"""Show pause menu"""
	if pause_menu_panel:
		pause_menu_panel.visible = true
	print("⏸️ Showing pause menu")

func show_shop():
	"""Show shop interface"""
	if shop_panel:
		shop_panel.visible = true
	print("🛒 Showing shop")

# HUD Update Methods
func update_speed_display(speed: float):
	"""Update speedometer display"""
	if speed_label:
		speed_label.text = "Speed: %.0f km/h" % speed

func update_fuel_display(fuel_percentage: float):
	"""Update fuel gauge"""
	if fuel_bar:
		fuel_bar.value = fuel_percentage * 100
		
		# Change color based on fuel level
		if fuel_percentage < 0.2:
			fuel_bar.modulate = Color.RED
		elif fuel_percentage < 0.5:
			fuel_bar.modulate = Color.YELLOW
		else:
			fuel_bar.modulate = Color.GREEN

func update_damage_display(damage_percentage: float):
	"""Update damage indicator"""
	if damage_bar:
		damage_bar.value = damage_percentage * 100
		damage_bar.modulate = Color.lerp(Color.GREEN, Color.RED, damage_percentage)

func update_timer_display(remaining_time: float):
	"""Update mission timer"""
	if timer_label:
		var minutes = int(remaining_time / 60)
		var seconds = int(remaining_time) % 60
		timer_label.text = "Time: %02d:%02d" % [minutes, seconds]
		
		# Change color when time is running low
		if remaining_time < 60:
			timer_label.modulate = Color.RED
		elif remaining_time < 180:
			timer_label.modulate = Color.YELLOW
		else:
			timer_label.modulate = Color.WHITE

func update_money_display(money: float):
	"""Update money display"""
	if money_label:
		money_label.text = "Money: $%.0f" % money

func update_gps_display(direction: String):
	"""Update GPS directions"""
	if gps_label:
		gps_label.text = direction

# Button Event Handlers
func _on_new_mission_pressed():
	"""Handle new mission button"""
	print("🎯 New mission requested")
	mission_start_requested.emit("Downtown Plaza", "Shopping Mall")

func _on_continue_pressed():
	"""Handle continue button"""
	print("▶️ Continue game")
	# Load saved game
	var game_manager = get_tree().get_first_node_in_group("game_manager")
	if game_manager:
		game_manager.load_game()

func _on_settings_pressed():
	"""Handle settings button"""
	print("⚙️ Settings opened")
	# Show settings panel (to be implemented)

func _on_quit_pressed():
	"""Handle quit button"""
	print("👋 Quitting game")
	get_tree().quit()

func _on_start_mission_pressed():
	"""Handle start mission button"""
	print("🚀 Starting mission")
	var game_manager = get_tree().get_first_node_in_group("game_manager")
	if game_manager and game_manager.current_mission:
		game_manager.start_mission(game_manager.current_mission)

func _on_cancel_mission_pressed():
	"""Handle cancel mission button"""
	print("❌ Mission cancelled")
	show_main_menu()

# Input handling
func _input(event):
	"""Handle UI input"""
	if event.is_action_pressed("ui_cancel"):
		if driving_hud_panel and driving_hud_panel.visible:
			# Show pause menu
			var game_manager = get_tree().get_first_node_in_group("game_manager")
			if game_manager:
				game_manager.change_game_state(GameManager.GameState.PAUSED)
