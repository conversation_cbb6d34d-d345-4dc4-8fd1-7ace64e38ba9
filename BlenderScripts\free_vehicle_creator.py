# FREE Vehicle Creator for Blender
# Creates realistic vehicles using only free tools and techniques

import bpy
import bmesh
from mathutils import Vector

def create_free_car(name="FreeCar"):
    """Create a realistic car using free Blender tools"""
    print(f"🚗 Creating free vehicle: {name}")
ECHO is off.
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
ECHO is off.
    # Create car body
    bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
    car = bpy.context.active_object
    car.name = f"{name}_Body"
    car.scale = (2, 4, 1)
    bpy.ops.object.transform_apply(scale=True)
ECHO is off.
    # Add subdivision for smooth surfaces
    bpy.context.view_layer.objects.active = car
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
ECHO is off.
    # Create wheels
    wheel_positions = [
        (-1.5, 1.5, 0.4), (1.5, 1.5, 0.4),
        (-1.5, -1.5, 0.4), (1.5, -1.5, 0.4)
    ]
ECHO is off.
    for i, pos in enumerate(wheel_positions):
        bpy.ops.mesh.primitive_cylinder_add(radius=0.4, depth=0.3, location=pos)
        wheel = bpy.context.active_object
        wheel.name = f"{name}_Wheel_{i+1}"
        wheel.rotation_euler = (1.5708, 0, 0)
        wheel.parent = car
ECHO is off.
    print(f"✅ Free vehicle {name} created successfully")
    return car

def create_free_materials():
    """Create realistic materials using free Blender nodes"""
    materials = {}
ECHO is off.
    # Car paint material
    mat = bpy.data.materials.new(name='FreePaint')
    mat.use_nodes = True
    bsdf = mat.node_tree.nodes["Principled BSDF"]
    bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Red
    bsdf.inputs[6].default_value = 0.9  # Metallic
    bsdf.inputs[9].default_value = 0.1  # Roughness
    materials['paint'] = mat
ECHO is off.
    # Tire material
    mat = bpy.data.materials.new(name='FreeTire')
    mat.use_nodes = True
    bsdf = mat.node_tree.nodes["Principled BSDF"]
    bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)
    bsdf.inputs[9].default_value = 0.8
    materials['tire'] = mat
ECHO is off.
    return materials

def export_for_godot(obj_name):
    """Export model for Godot engine"""
    obj = bpy.data.objects.get(obj_name)
    if obj:
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        bpy.context.view_layer.objects.active = obj
ECHO is off.
        # Export as GLTF for Godot
        bpy.ops.export_scene.gltf(
            filepath=f"FreeAssets/Models/{obj_name}.gltf",
            use_selection=True,
            export_format='GLTF_SEPARATE'
        )
        print(f"✅ Exported {obj_name} for Godot")

if __name__ == "__main__":
    # Create free vehicles
    car = create_free_car("Sedan")
    materials = create_free_materials()
ECHO is off.
    # Apply materials
    car.data.materials.append(materials['paint'])
ECHO is off.
    # Export for Godot
    export_for_godot("Sedan_Body")
ECHO is off.
    print("🎉 Free vehicle creation complete!")
