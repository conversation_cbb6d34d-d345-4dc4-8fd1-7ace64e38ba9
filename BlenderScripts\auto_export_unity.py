"""
Blender Auto-Export Script for Unity Integration
Automatically exports selected objects with Unity-optimized settings
"""

import bpy
import os
import bmesh
from mathutils import Vector

class UnityExporter:
    def __init__(self):
        self.export_path = "//Exports/Unity_Ready/"
        self.ensure_export_directory()
    
    def ensure_export_directory(self):
        """Create export directory if it doesn't exist"""
        abs_path = bpy.path.abspath(self.export_path)
        os.makedirs(abs_path, exist_ok=True)
    
    def export_object_for_unity(self, obj_name, category="General"):
        """Export a single object with Unity-optimized settings"""
        obj = bpy.data.objects.get(obj_name)
        if not obj:
            print(f"Object '{obj_name}' not found")
            return False
        
        # Select only the target object
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        bpy.context.view_layer.objects.active = obj
        
        # Create category subdirectory
        category_path = os.path.join(self.export_path, category)
        abs_category_path = bpy.path.abspath(category_path)
        os.makedirs(abs_category_path, exist_ok=True)
        
        # Export path
        export_filepath = os.path.join(category_path, f"{obj_name}.fbx")
        abs_export_path = bpy.path.abspath(export_filepath)
        
        # Export with Unity settings
        try:
            bpy.ops.export_scene.fbx(
                filepath=abs_export_path,
                use_selection=True,
                global_scale=1.0,
                axis_forward='-Z',
                axis_up='Y',
                apply_modifiers=True,
                mesh_smooth_type='FACE',
                use_mesh_modifiers=True,
                use_armature_deform_only=True,
                add_leaf_bones=False,
                primary_bone_axis='Y',
                secondary_bone_axis='X',
                use_space_transform=True,
                bake_space_transform=False
            )
            print(f"✅ Exported {obj_name} to {export_filepath}")
            return True
        except Exception as e:
            print(f"❌ Failed to export {obj_name}: {str(e)}")
            return False
    
    def export_all_vehicles(self):
        """Export all vehicle objects"""
        vehicles = [obj.name for obj in bpy.data.objects 
                   if obj.name.startswith(('Car_', 'Vehicle_', 'Sedan', 'SUV', 'Sports'))]
        
        for vehicle in vehicles:
            self.export_object_for_unity(vehicle, "Vehicles")
    
    def export_all_buildings(self):
        """Export all building objects"""
        buildings = [obj.name for obj in bpy.data.objects 
                    if obj.name.startswith(('Building_', 'House_', 'Shop_', 'GasStation', 'Hospital'))]
        
        for building in buildings:
            self.export_object_for_unity(building, "Buildings")
    
    def export_all_props(self):
        """Export all prop objects"""
        props = [obj.name for obj in bpy.data.objects 
                if obj.name.startswith(('Prop_', 'Tree_', 'Light_', 'Sign_'))]
        
        for prop in props:
            self.export_object_for_unity(prop, "Props")
    
    def optimize_for_unity(self, obj_name):
        """Optimize object for Unity (reduce polygons, clean up)"""
        obj = bpy.data.objects.get(obj_name)
        if not obj or obj.type != 'MESH':
            return False
        
        # Switch to Edit mode
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Remove doubles
        bpy.ops.mesh.remove_doubles(threshold=0.001)
        
        # Recalculate normals
        bpy.ops.mesh.normals_make_consistent(inside=False)
        
        # Triangulate (Unity prefers triangles)
        bpy.ops.mesh.quads_convert_to_tris()
        
        # Switch back to Object mode
        bpy.ops.object.mode_set(mode='OBJECT')
        
        print(f"✅ Optimized {obj_name} for Unity")
        return True

class MaterialSetup:
    """Setup PBR materials for Unity compatibility"""
    
    @staticmethod
    def create_pbr_material(name, base_color=(0.8, 0.8, 0.8), metallic=0.0, roughness=0.5):
        """Create a PBR material with specified properties"""
        mat = bpy.data.materials.new(name=name)
        mat.use_nodes = True
        
        # Clear default nodes
        mat.node_tree.nodes.clear()
        
        # Add Principled BSDF
        bsdf = mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
        bsdf.inputs['Base Color'].default_value = (*base_color, 1.0)
        bsdf.inputs['Metallic'].default_value = metallic
        bsdf.inputs['Roughness'].default_value = roughness
        
        # Add Material Output
        output = mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
        mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
        
        return mat
    
    @staticmethod
    def setup_car_materials():
        """Create standard car materials"""
        materials = {
            'CarPaint_Red': MaterialSetup.create_pbr_material('CarPaint_Red', (0.8, 0.1, 0.1), 0.9, 0.1),
            'CarPaint_Blue': MaterialSetup.create_pbr_material('CarPaint_Blue', (0.1, 0.1, 0.8), 0.9, 0.1),
            'CarPaint_White': MaterialSetup.create_pbr_material('CarPaint_White', (0.9, 0.9, 0.9), 0.9, 0.1),
            'Tire_Rubber': MaterialSetup.create_pbr_material('Tire_Rubber', (0.1, 0.1, 0.1), 0.0, 0.8),
            'Chrome': MaterialSetup.create_pbr_material('Chrome', (0.9, 0.9, 0.9), 1.0, 0.0),
            'Glass': MaterialSetup.create_pbr_material('Glass', (0.9, 0.9, 0.9), 0.0, 0.0),
            'Plastic_Black': MaterialSetup.create_pbr_material('Plastic_Black', (0.1, 0.1, 0.1), 0.0, 0.3)
        }
        
        # Make glass transparent
        materials['Glass'].blend_method = 'BLEND'
        materials['Glass'].node_tree.nodes['Principled BSDF'].inputs['Alpha'].default_value = 0.3
        
        print("✅ Created standard car materials")
        return materials

class VehicleCreator:
    """Helper class for creating vehicle models"""
    
    @staticmethod
    def create_basic_car(name="BasicCar"):
        """Create a basic car shape as starting point"""
        # Add cube for car body
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
        car_body = bpy.context.active_object
        car_body.name = f"{name}_Body"
        
        # Scale to car proportions
        car_body.scale = (2, 4.5, 1)
        bpy.ops.object.transform_apply(scale=True)
        
        # Add wheels
        wheel_positions = [
            (-1.5, 1.5, 0.3),   # Front Left
            (1.5, 1.5, 0.3),    # Front Right
            (-1.5, -1.5, 0.3),  # Rear Left
            (1.5, -1.5, 0.3)    # Rear Right
        ]
        
        wheels = []
        for i, pos in enumerate(wheel_positions):
            bpy.ops.mesh.primitive_cylinder_add(radius=0.4, depth=0.3, location=pos)
            wheel = bpy.context.active_object
            wheel.name = f"{name}_Wheel_{i+1}"
            wheel.rotation_euler = (1.5708, 0, 0)  # Rotate 90 degrees
            wheels.append(wheel)
        
        # Parent wheels to car body
        for wheel in wheels:
            wheel.parent = car_body
        
        print(f"✅ Created basic car: {name}")
        return car_body, wheels

class BuildingCreator:
    """Helper class for creating building models"""
    
    @staticmethod
    def create_gas_station(name="GasStation"):
        """Create a basic gas station model"""
        # Main building
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 2))
        main_building = bpy.context.active_object
        main_building.name = f"{name}_Building"
        main_building.scale = (4, 3, 2)
        bpy.ops.object.transform_apply(scale=True)
        
        # Canopy
        bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 5, 3))
        canopy = bpy.context.active_object
        canopy.name = f"{name}_Canopy"
        canopy.scale = (6, 4, 0.2)
        bpy.ops.object.transform_apply(scale=True)
        
        # Fuel pumps
        pump_positions = [(-2, 5, 0.75), (2, 5, 0.75)]
        pumps = []
        
        for i, pos in enumerate(pump_positions):
            bpy.ops.mesh.primitive_cube_add(size=2, location=pos)
            pump = bpy.context.active_object
            pump.name = f"{name}_Pump_{i+1}"
            pump.scale = (0.3, 0.5, 0.75)
            bpy.ops.object.transform_apply(scale=True)
            pumps.append(pump)
        
        # Parent all to main building
        canopy.parent = main_building
        for pump in pumps:
            pump.parent = main_building
        
        print(f"✅ Created gas station: {name}")
        return main_building

# Main execution functions
def setup_driving_simulator_assets():
    """Main function to set up all assets for the driving simulator"""
    print("🚗 Setting up Driving Simulator Assets in Blender")
    print("=" * 50)
    
    # Clear existing mesh objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)
    
    # Create materials
    MaterialSetup.setup_car_materials()
    
    # Create vehicles
    VehicleCreator.create_basic_car("Sedan")
    VehicleCreator.create_basic_car("SUV")
    
    # Create buildings
    BuildingCreator.create_gas_station("GasStation_01")
    
    print("=" * 50)
    print("✅ Asset setup complete!")
    print("Next steps:")
    print("1. Refine models with more detail")
    print("2. Apply materials to objects")
    print("3. UV unwrap for texturing")
    print("4. Export to Unity using UnityExporter")

def export_all_assets():
    """Export all assets for Unity"""
    exporter = UnityExporter()
    
    print("🚀 Exporting all assets for Unity...")
    
    # Export vehicles
    exporter.export_all_vehicles()
    
    # Export buildings
    exporter.export_all_buildings()
    
    # Export props
    exporter.export_all_props()
    
    print("✅ Export complete!")

# Register functions for Blender UI
def register():
    pass

def unregister():
    pass

if __name__ == "__main__":
    # Run setup when script is executed
    setup_driving_simulator_assets()
