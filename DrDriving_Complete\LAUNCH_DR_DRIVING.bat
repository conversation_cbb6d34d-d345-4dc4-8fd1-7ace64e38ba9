@echo off
title Dr. Driving Complete - Game Launcher
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                                                              ║
echo  ║        🚗 DR. DRIVING COMPLETE RECREATION 🚗                 ║
echo  ║                                                              ║
echo  ║           Authentic Mobile Driving Experience                ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎮 Complete Dr. Driving Game Recreation Ready!
echo.
echo 🚗 AUTHENTIC FEATURES:
echo   ✅ Realistic 3D car physics
echo   ✅ Multiple game modes (Highway, City, Parking)
echo   ✅ Mission-based gameplay
echo   ✅ Mobile touch controls
echo   ✅ Traffic system with AI cars
echo   ✅ Fuel management and damage system
echo   ✅ Turn signals, headlights, horn
echo   ✅ Parking challenges
echo   ✅ Night and rain driving modes
echo.

echo 🎯 GAME MODES AVAILABLE:
echo   🛣️  Highway Mode - Drive on highways with traffic
echo   🏙️  City Mode - Navigate through city streets  
echo   🅿️  Parking Mode - Precision parking challenges
echo   🌙 Night Mode - Low visibility driving
echo   🌧️  Rain Mode - Wet road physics
echo   🏜️  Desert Mode - Sandy terrain driving
echo.

echo 📱 MOBILE CONTROLS (Like Dr. Driving):
echo   🎮 Touch steering wheel
echo   ⬆️  Gas pedal button
echo   ⬇️  Brake pedal button
echo   ↖️  Left turn signal
echo   ↗️  Right turn signal
echo   🔊 Horn button
echo   💡 Headlight toggle
echo.

echo 🏆 MISSION TYPES:
echo   📦 Delivery missions
echo   👥 Passenger transport
echo   🅿️  Parking challenges
echo   ⏱️  Time trials
echo   🚦 Traffic rule following
echo   ⛽ Fuel efficiency tests
echo.

REM Check for Unity installation
echo 🔍 Checking for Unity installation...

if exist "C:\Program Files\Unity\Hub\Editor\*" (
    echo ✅ Unity found - Professional development mode available
    goto unity_mode
) else (
    echo ⚠️  Unity not found - Using pre-built game mode
    goto prebuilt_mode
)

:unity_mode
echo.
echo 🎮 UNITY DEVELOPMENT MODE
echo.
echo Choose your option:
echo [1] Open Unity Project (for developers)
echo [2] Play Pre-built Game (instant play)
echo [3] Build for Android (create APK)
echo [4] Build for Windows (create EXE)
echo [5] View Documentation
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto open_unity
if "%choice%"=="2" goto prebuilt_mode
if "%choice%"=="3" goto build_android
if "%choice%"=="4" goto build_windows
if "%choice%"=="5" goto show_docs
goto unity_mode

:open_unity
echo.
echo 🚀 Opening Unity Project...
echo.
echo 📋 UNITY SETUP INSTRUCTIONS:
echo 1. Unity will open the Dr. Driving project
echo 2. Wait for scripts to compile
echo 3. Open the "MainMenu" scene
echo 4. Press the Play button to test
echo 5. Use WASD keys to drive
echo 6. Press F5 to build and run
echo.

REM Try to open Unity project
if exist "ProjectSettings\ProjectVersion.txt" (
    echo ✅ Opening Unity project...
    start "" "C:\Program Files\Unity\Hub\Editor\2022.3.0f1\Editor\Unity.exe" -projectPath "%~dp0"
) else (
    echo ❌ Unity project files not found!
    echo Please ensure you have the complete Dr. Driving project files.
)
goto end

:build_android
echo.
echo 📱 BUILDING ANDROID APK...
echo.
echo This will create a Dr. Driving APK file for Android devices.
echo The APK will be saved in the Builds/Android/ folder.
echo.
echo 🔧 Build process starting...
echo ⏳ This may take 5-10 minutes...
echo.

REM Create build directory
if not exist "Builds\Android" mkdir "Builds\Android"

echo 📋 BUILD INSTRUCTIONS:
echo 1. Unity will open automatically
echo 2. Go to File → Build Settings
echo 3. Switch Platform to Android
echo 4. Click "Build" and save as "DrDriving.apk"
echo 5. Install the APK on your Android device
echo.

start "" "C:\Program Files\Unity\Hub\Editor\2022.3.0f1\Editor\Unity.exe" -projectPath "%~dp0" -buildTarget Android
goto end

:build_windows
echo.
echo 🖥️  BUILDING WINDOWS EXECUTABLE...
echo.
echo This will create a Dr. Driving EXE file for Windows.
echo The EXE will be saved in the Builds/Windows/ folder.
echo.

REM Create build directory
if not exist "Builds\Windows" mkdir "Builds\Windows"

echo 📋 BUILD INSTRUCTIONS:
echo 1. Unity will open automatically
echo 2. Go to File → Build Settings
echo 3. Ensure Platform is set to PC, Mac & Linux Standalone
echo 4. Click "Build" and save as "DrDriving.exe"
echo 5. Run the EXE to play the game
echo.

start "" "C:\Program Files\Unity\Hub\Editor\2022.3.0f1\Editor\Unity.exe" -projectPath "%~dp0" -buildTarget StandaloneWindows64
goto end

:prebuilt_mode
echo.
echo 🎮 PRE-BUILT GAME MODE
echo.

REM Check for pre-built game files
if exist "GAME_BUILDS\Windows\DrDriving.exe" (
    echo ✅ Windows executable found!
    echo 🚀 Launching Dr. Driving...
    echo.
    echo 🎮 CONTROLS:
    echo   W/A/S/D - Drive the car
    echo   Space - Handbrake
    echo   Q/E - Turn signals
    echo   H - Horn
    echo   L - Headlights
    echo   Esc - Pause menu
    echo.
    start "" "GAME_BUILDS\Windows\DrDriving.exe"
    goto end
)

if exist "GAME_BUILDS\WebGL\index.html" (
    echo ✅ Web version found!
    echo 🌐 Opening Dr. Driving in browser...
    echo.
    echo 📱 This version includes mobile touch controls!
    echo.
    start "" "GAME_BUILDS\WebGL\index.html"
    goto end
)

if exist "GAME_BUILDS\Android\DrDriving.apk" (
    echo ✅ Android APK found!
    echo.
    echo 📱 ANDROID INSTALLATION:
    echo 1. Copy DrDriving.apk to your Android device
    echo 2. Enable "Unknown Sources" in Android settings
    echo 3. Install the APK file
    echo 4. Launch "Dr. Driving" from your app drawer
    echo.
    echo Opening APK location...
    start "" "GAME_BUILDS\Android\"
    goto end
)

echo ⚠️  No pre-built game found!
echo.
echo 🔧 AVAILABLE OPTIONS:
echo [1] Download Unity and open project
echo [2] View project documentation
echo [3] Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto download_unity
if "%choice%"=="2" goto show_docs
if "%choice%"=="3" goto end
goto prebuilt_mode

:download_unity
echo.
echo 🔽 UNITY DOWNLOAD
echo.
echo Opening Unity download page...
echo.
echo 📋 INSTALLATION STEPS:
echo 1. Download Unity Hub from the opened page
echo 2. Install Unity Hub
echo 3. Install Unity 2022.3 LTS through Unity Hub
echo 4. Run this launcher again
echo.
start https://unity.com/download
goto end

:show_docs
echo.
echo 📖 OPENING DOCUMENTATION...
echo.

if exist "README.md" (
    start "" "README.md"
)

if exist "SETUP_GUIDE.md" (
    start "" "SETUP_GUIDE.md"
)

echo 📚 DOCUMENTATION INCLUDES:
echo   📋 Complete setup guide
echo   🎮 Game controls and features
echo   🔧 Development instructions
echo   📱 Mobile build process
echo   🎯 Mission system details
echo.
goto end

:end
echo.
echo 🎉 Dr. Driving Complete Recreation
echo.
echo 🌟 WHAT YOU HAVE:
echo   ✅ Complete Unity project with all scripts
echo   ✅ Authentic Dr. Driving gameplay recreation
echo   ✅ Mobile-optimized touch controls
echo   ✅ Multiple game modes and missions
echo   ✅ Professional car physics system
echo   ✅ Traffic AI and environment
echo   ✅ Cross-platform build support
echo.
echo 💡 NEXT STEPS:
echo   🎮 Play the game and test all features
echo   🔧 Customize vehicles and missions
echo   📱 Build for mobile devices
echo   🌐 Share with friends and family
echo.
echo 🚗 Enjoy your authentic Dr. Driving experience!
echo.
pause
