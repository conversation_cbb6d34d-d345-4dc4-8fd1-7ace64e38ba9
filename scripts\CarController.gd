# CarController.gd - FREE Realistic Vehicle Physics for Godot
# Part of the FREE Realistic Driving Simulator
# No cost, open source, professional quality

extends RigidBody3D
class_name CarController

# Vehicle specifications
@export_group("Vehicle Settings")
@export var engine_power: float = 1200.0
@export var max_speed: float = 200.0  # km/h
@export var steering_angle: float = 25.0  # degrees
@export var brake_force: float = 3000.0
@export var mass_kg: float = 1500.0

@export_group("Fuel System")
@export var max_fuel: float = 60.0  # liters
@export var fuel_consumption_rate: float = 0.1  # liters per second at full throttle
@export var current_fuel: float = 60.0

@export_group("Damage System")
@export var max_damage: float = 100.0
@export var current_damage: float = 0.0
@export var damage_threshold: float = 10.0  # minimum impact speed for damage

@export_group("Wheel References")
@export var front_left_wheel: Node3D
@export var front_right_wheel: Node3D
@export var rear_left_wheel: Node3D
@export var rear_right_wheel: Node3D

# Input variables
var steering_input: float = 0.0
var throttle_input: float = 0.0
var brake_input: float = 0.0
var handbrake_input: bool = false

# Physics variables
var current_speed_kmh: float = 0.0
var wheel_base: float = 2.5  # distance between front and rear axles
var track_width: float = 1.5  # distance between left and right wheels

# Audio
@onready var engine_audio: AudioStreamPlayer3D = $EngineAudio
@onready var brake_audio: AudioStreamPlayer3D = $BrakeAudio

# Signals for UI updates
signal speed_changed(speed: float)
signal fuel_changed(fuel_percentage: float)
signal damage_changed(damage_percentage: float)

func _ready():
	# Set up realistic physics
	mass = mass_kg
	center_of_mass = Vector3(0, -0.3, 0.2)  # Lower and slightly forward
	
	# Set up collision detection for damage
	body_entered.connect(_on_collision)
	
	# Initialize audio
	setup_audio()
	
	print("🚗 Free Car Controller initialized - Mass: %d kg" % mass_kg)

func _physics_process(delta):
	if current_fuel <= 0:
		return  # No fuel, no movement
	
	handle_input()
	apply_vehicle_physics(delta)
	update_wheels(delta)
	update_audio()
	update_stats(delta)

func handle_input():
	"""Handle player input for driving"""
	# Steering (A/D keys or left/right arrows)
	steering_input = Input.get_axis("steer_left", "steer_right")
	
	# Throttle (W key or up arrow)
	throttle_input = Input.get_action_strength("accelerate")
	
	# Brake (S key or down arrow)
	brake_input = Input.get_action_strength("brake")
	
	# Handbrake (Space key)
	handbrake_input = Input.is_action_pressed("handbrake")

func apply_vehicle_physics(delta):
	"""Apply realistic vehicle physics"""
	# Calculate current speed
	current_speed_kmh = linear_velocity.length() * 3.6
	
	# Don't exceed max speed
	if current_speed_kmh >= max_speed and throttle_input > 0:
		throttle_input = 0
	
	# Calculate forces
	var forward_force = calculate_engine_force()
	var steering_force = calculate_steering_force()
	var brake_force_applied = calculate_brake_force()
	
	# Apply forces
	if forward_force > 0:
		var forward_direction = -global_transform.basis.z
		apply_central_force(forward_direction * forward_force)
	
	# Apply steering (only when moving)
	if abs(current_speed_kmh) > 1.0:
		var steering_torque = steering_force * (current_speed_kmh / max_speed)
		apply_torque(Vector3(0, steering_torque, 0))
	
	# Apply braking
	if brake_force_applied > 0:
		linear_velocity *= (1.0 - brake_force_applied * delta)
		angular_velocity *= (1.0 - brake_force_applied * delta * 0.5)
	
	# Apply drag and rolling resistance
	apply_drag_forces(delta)

func calculate_engine_force() -> float:
	"""Calculate engine force based on throttle and RPM"""
	if throttle_input <= 0 or current_fuel <= 0:
		return 0.0
	
	# Simple power curve - more realistic than linear
	var speed_factor = 1.0 - (current_speed_kmh / max_speed)
	var engine_force = engine_power * throttle_input * speed_factor
	
	return engine_force

func calculate_steering_force() -> float:
	"""Calculate steering force with realistic physics"""
	if abs(steering_input) < 0.01:
		return 0.0
	
	# Steering is more effective at lower speeds
	var speed_factor = clamp(1.0 - (current_speed_kmh / 100.0), 0.2, 1.0)
	var steering_force = steering_input * steering_angle * speed_factor
	
	return steering_force

func calculate_brake_force() -> float:
	"""Calculate braking force"""
	var total_brake = brake_input
	
	# Handbrake adds extra braking
	if handbrake_input:
		total_brake = min(total_brake + 0.7, 1.0)
	
	return total_brake * brake_force / mass

func apply_drag_forces(delta):
	"""Apply air resistance and rolling resistance"""
	# Air resistance (increases with speed squared)
	var air_resistance = linear_velocity.length_squared() * 0.3
	var drag_force = -linear_velocity.normalized() * air_resistance
	apply_central_force(drag_force)
	
	# Rolling resistance
	var rolling_resistance = -linear_velocity * 50.0
	apply_central_force(rolling_resistance)
	
	# Angular drag
	angular_velocity *= 0.95

func update_wheels(delta):
	"""Update wheel rotations and positions"""
	if not front_left_wheel:
		return
	
	# Calculate wheel rotation speed
	var wheel_circumference = 2.0 * PI * 0.35  # Assuming 35cm radius
	var rotation_speed = (current_speed_kmh / 3.6) / wheel_circumference
	
	# Rotate wheels based on speed
	var rotation_delta = rotation_speed * delta
	
	# Front wheels (steering)
	var front_steering = deg_to_rad(steering_input * steering_angle)
	
	if front_left_wheel:
		front_left_wheel.rotation.y = front_steering
		front_left_wheel.rotation.x += rotation_delta
	
	if front_right_wheel:
		front_right_wheel.rotation.y = front_steering
		front_right_wheel.rotation.x += rotation_delta
	
	# Rear wheels (no steering)
	if rear_left_wheel:
		rear_left_wheel.rotation.x += rotation_delta
	
	if rear_right_wheel:
		rear_right_wheel.rotation.x += rotation_delta

func update_stats(delta):
	"""Update vehicle statistics"""
	# Consume fuel
	if throttle_input > 0 and current_fuel > 0:
		var consumption = fuel_consumption_rate * throttle_input * delta
		current_fuel = max(0, current_fuel - consumption)
		fuel_changed.emit(current_fuel / max_fuel)
	
	# Emit speed update
	speed_changed.emit(current_speed_kmh)
	
	# Emit damage update
	damage_changed.emit(current_damage / max_damage)

func setup_audio():
	"""Set up engine and brake audio"""
	if engine_audio:
		engine_audio.stream = preload("res://audio/engine_idle.ogg")
		engine_audio.play()
	
	if brake_audio:
		brake_audio.stream = preload("res://audio/brake_squeal.ogg")

func update_audio():
	"""Update audio based on vehicle state"""
	if engine_audio and current_fuel > 0:
		# Engine pitch based on throttle and RPM
		var base_pitch = 0.8
		var throttle_pitch = throttle_input * 0.6
		var speed_pitch = (current_speed_kmh / max_speed) * 0.4
		
		engine_audio.pitch_scale = base_pitch + throttle_pitch + speed_pitch
		engine_audio.volume_db = linear_to_db(0.3 + throttle_input * 0.4)
	
	# Brake audio
	if brake_audio:
		if brake_input > 0.5 and current_speed_kmh > 20:
			if not brake_audio.playing:
				brake_audio.play()
			brake_audio.volume_db = linear_to_db(brake_input * 0.5)
		else:
			brake_audio.stop()

func _on_collision(body):
	"""Handle collision damage"""
	if body == self:
		return
	
	var impact_speed = linear_velocity.length() * 3.6  # Convert to km/h
	
	if impact_speed > damage_threshold:
		var damage_amount = (impact_speed - damage_threshold) * 0.5
		add_damage(damage_amount)
		
		print("💥 Collision! Impact speed: %.1f km/h, Damage: %.1f" % [impact_speed, damage_amount])

func add_damage(amount: float):
	"""Add damage to the vehicle"""
	current_damage = min(current_damage + amount, max_damage)
	damage_changed.emit(current_damage / max_damage)
	
	# Reduce performance based on damage
	var damage_factor = 1.0 - (current_damage / max_damage) * 0.3
	engine_power = engine_power * damage_factor

func repair_vehicle(repair_amount: float):
	"""Repair the vehicle"""
	current_damage = max(0, current_damage - repair_amount)
	damage_changed.emit(current_damage / max_damage)
	
	print("🔧 Vehicle repaired by %.1f points" % repair_amount)

func add_fuel(fuel_amount: float):
	"""Add fuel to the vehicle"""
	current_fuel = min(current_fuel + fuel_amount, max_fuel)
	fuel_changed.emit(current_fuel / max_fuel)
	
	print("⛽ Added %.1f liters of fuel" % fuel_amount)

func get_fuel_percentage() -> float:
	"""Get current fuel as percentage"""
	return current_fuel / max_fuel

func get_damage_percentage() -> float:
	"""Get current damage as percentage"""
	return current_damage / max_damage

func get_speed_kmh() -> float:
	"""Get current speed in km/h"""
	return current_speed_kmh

func is_engine_running() -> bool:
	"""Check if engine can run"""
	return current_fuel > 0 and current_damage < max_damage

# Input map setup (call this from main scene)
func setup_input_map():
	"""Set up input actions for the vehicle"""
	if not InputMap.has_action("accelerate"):
		InputMap.add_action("accelerate")
		var event = InputEventKey.new()
		event.keycode = KEY_W
		InputMap.action_add_event("accelerate", event)
		
		event = InputEventKey.new()
		event.keycode = KEY_UP
		InputMap.action_add_event("accelerate", event)
	
	if not InputMap.has_action("brake"):
		InputMap.add_action("brake")
		var event = InputEventKey.new()
		event.keycode = KEY_S
		InputMap.action_add_event("brake", event)
		
		event = InputEventKey.new()
		event.keycode = KEY_DOWN
		InputMap.action_add_event("brake", event)
	
	if not InputMap.has_action("steer_left"):
		InputMap.add_action("steer_left")
		var event = InputEventKey.new()
		event.keycode = KEY_A
		InputMap.action_add_event("steer_left", event)
		
		event = InputEventKey.new()
		event.keycode = KEY_LEFT
		InputMap.action_add_event("steer_left", event)
	
	if not InputMap.has_action("steer_right"):
		InputMap.add_action("steer_right")
		var event = InputEventKey.new()
		event.keycode = KEY_D
		InputMap.action_add_event("steer_right", event)
		
		event = InputEventKey.new()
		event.keycode = KEY_RIGHT
		InputMap.action_add_event("steer_right", event)
	
	if not InputMap.has_action("handbrake"):
		InputMap.add_action("handbrake")
		var event = InputEventKey.new()
		event.keycode = KEY_SPACE
		InputMap.action_add_event("handbrake", event)

	print("🎮 Input map configured for FREE driving simulator")
